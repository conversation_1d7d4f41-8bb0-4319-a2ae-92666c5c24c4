# 分镜图片生成器使用说明

## 🎯 功能概述

分镜图片生成器是小说AI文案生成器的重要组成部分，专门用于将分镜脚本转换为图片，支持完整的图片生成工作流。

## 🚀 启动方式

1. **从主界面启动**
   - 在主界面点击 "工具" → "分镜图片生成"

2. **直接启动**
   - 运行 `python main.py` 后打开分镜图片生成器

## 📋 界面布局

### 左侧控制面板

#### 1. 脚本输入区域
- **导入脚本按钮** - 从文件导入分镜脚本
- **解析脚本按钮** - 解析输入的分镜内容
- **脚本输入框** - 手动输入或编辑分镜脚本

#### 2. 生成设置
- **图片风格选择** - realistic(写实), anime(动漫), cartoon(卡通)
- **图片尺寸选择** - 1024x1024, 1024x768, 768x1024等

#### 3. 分镜场景列表
- 显示解析后的所有场景
- 点击场景可查看详细信息
- **生成当前** - 生成选中场景的图片
- **生成全部** - 批量生成所有场景图片

#### 4. 当前场景信息
- **场景标题** - 显示当前选中的场景
- **提示词显示** - 自动生成的图片提示词
- **优化提示词** - 使用AI优化提示词
- **测试生成** - 测试图片生成功能

### 右侧显示区域

#### 标签页1: 豆包生图
- **内嵌豆包网页** - 直接在应用内使用豆包生图
- **刷新按钮** - 刷新网页
- **主页按钮** - 返回豆包首页
- **网页导航** - 完整的浏览器功能

#### 标签页2: 生成结果 ✅ 已修复
- **图片显示区域** - 显示生成的图片或链接
- **保存图片按钮** - 保存当前图片
- **复制提示词按钮** - 复制当前提示词
- **图片信息显示** - 显示风格、时间、提示词等信息

#### 标签页3: 图片库
- **图片列表** - 显示所有生成的图片
- **清空图片库** - 清除所有记录
- **导出全部** - 导出所有图片
- **点击查看** - 点击列表项查看图片详情

## 🔧 使用流程

### 方法1: 导入分镜脚本

1. **准备分镜脚本文件**
   ```
   【镜头1】
   场景：古代房间
   构图：中景
   内容：一个古装美女推门而入
   时长：3秒
   备注：表情温柔

   【镜头2】
   场景：庭院
   构图：远景
   内容：美女在花园中漫步
   时长：5秒
   备注：背景有桃花
   ```

2. **导入和解析**
   - 点击"导入脚本"选择文件
   - 或直接在输入框中粘贴内容
   - 点击"解析脚本"

3. **生成图片**
   - 在场景列表中选择要生成的场景
   - 查看自动生成的提示词
   - 可选择"优化提示词"
   - 点击"生成当前"开始生成

### 方法2: 手动输入场景

1. **直接输入提示词**
   - 在"当前场景信息"区域直接输入描述
   - 选择合适的图片风格
   - 点击"测试生成"验证功能

2. **使用豆包生图**
   - 切换到"豆包生图"标签页
   - 在网页中输入提示词
   - 添加"请生成图片"指令
   - 等待豆包生成图片

## 🎨 生成结果功能详解

### 图片显示
- **网络图片** - 显示图片链接和使用说明
- **本地图片** - 直接显示图片预览
- **错误处理** - 显示友好的错误信息

### 保存功能
- **网络图片保存**
  - 复制链接到剪贴板
  - 在浏览器中打开图片
- **本地图片保存**
  - 选择保存位置
  - 复制到指定目录

### 信息管理
- **详细信息** - 风格、时间、提示词
- **历史记录** - 所有生成记录保存在图片库
- **快速复用** - 点击图片库项目快速查看

## 🔍 测试功能

### 测试生成按钮
- 点击"测试生成"可以：
  - 验证生成结果显示功能
  - 测试图片库记录功能
  - 检查保存和复制功能
  - 确认界面切换正常

### 测试结果
- 生成测试图片链接
- 自动切换到生成结果标签页
- 添加记录到图片库
- 显示功能说明对话框

## 🛠️ 故障排除

### 生成结果不显示
- ✅ **已修复** - 完善了图片显示逻辑
- ✅ **已修复** - 添加了详细的信息显示
- ✅ **已修复** - 改进了错误处理机制

### WebEngine问题
- 使用优化的启动脚本 `run_webengine.bat`
- 运行诊断工具 `python diagnose_webengine.py`
- 查看详细解决方案 `WebEngine解决方案.md`

### 图片保存问题
- 网络图片：复制链接后在浏览器中右键保存
- 本地图片：使用"保存图片"按钮选择位置
- 批量保存：使用"导出全部"功能

## 📊 功能状态

### ✅ 已完成功能
- 分镜脚本解析
- 图片提示词生成
- 豆包网页集成
- **生成结果显示** ← 刚刚修复
- 图片库管理
- 提示词优化
- 测试生成功能

### 🚧 开发中功能
- 批量图片生成
- 自动图片下载
- 更多图片风格
- 视频预览功能

## 💡 使用技巧

### 提示词优化
- 使用"优化提示词"按钮改进描述
- 添加艺术风格关键词
- 包含具体的构图信息
- 使用英文关键词效果更好

### 风格选择
- **realistic** - 适合写实风格的场景
- **anime** - 适合动漫风格的角色
- **cartoon** - 适合卡通风格的内容

### 工作流程建议
1. 先用"测试生成"验证功能
2. 导入完整的分镜脚本
3. 逐个场景生成和调整
4. 使用图片库管理所有结果
5. 批量导出最终图片

## 🎉 总结

分镜图片生成器现在具备完整的功能：
- ✅ 脚本解析和场景管理
- ✅ 智能提示词生成
- ✅ 内嵌网页图片生成
- ✅ **完善的生成结果显示**
- ✅ 图片库管理和导出

所有功能都已正常工作，您可以开始使用完整的分镜图片生成工作流了！

---

**提示**: 如果遇到任何问题，请使用"测试生成"按钮验证功能，或查看相关的故障排除文档。
