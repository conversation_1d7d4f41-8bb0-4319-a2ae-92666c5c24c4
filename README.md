# 小说AI文案生成器

一款基于Python 3.12和PySide6开发的智能文案生成工具，专门用于根据小说内容生成沙雕动画文案和爆款开头。

## 功能特点

- 🎬 **沙雕动画文案生成** - 根据小说内容生成适合短视频的动画文案
- 🔥 **爆款开头生成** - 生成吸引人的小说开头，提高阅读率
- 🎭 **智能分镜脚本** - 基于动画文案生成详细的分镜脚本
- 🤖 **多AI支持** - 支持OpenAI GPT、通义千问、DeepSeek、Gemini等多种AI模型
- 📝 **文本处理** - 智能文本清理、格式化和摘要提取
- 💾 **文件操作** - 支持文本文件导入导出
- ⚙️ **灵活配置** - 可自定义动画风格、开头类型等参数
- 📊 **实时统计** - 字数统计和生成进度显示
- 🎯 **完整工作流** - 从小说到文案再到分镜的完整视频制作流程

## 系统要求

- Python 3.12+
- Windows 10/11 (其他系统未测试)
- 至少2GB内存
- 网络连接 (用于AI API调用)

## 安装步骤

1. **克隆或下载项目**
   ```bash
   git clone <项目地址>
   cd 小说
   ```

2. **安装依赖**
   ```bash
   pip install -r requirements.txt
   ```

3. **配置AI API**
   - 运行程序后，点击"工具" -> "设置"
   - 选择AI提供商并输入API密钥
   - 配置生成参数

4. **运行程序**
   ```bash
   python main.py
   ```

## 使用指南

### 基本使用流程

1. **输入小说内容**
   - 直接在左侧文本框输入小说内容
   - 或点击"打开文件"导入txt文件

2. **选择生成类型**
   - 选择动画风格（搞笑沙雕、悬疑惊悚等）
   - 选择开头类型（悬念开头、对话开头等）

3. **生成文案**
   - 点击"生成动画文案"或"生成爆款开头"
   - 等待AI处理完成

4. **生成分镜脚本**
   - 在动画文案生成完成后，点击"生成分镜"
   - 系统会基于文案自动生成详细的分镜脚本

5. **查看和保存结果**
   - 在右侧标签页查看生成结果
   - 可复制到剪贴板或保存为文件

### 高级功能

#### AI设置
- **OpenAI配置**: 支持GPT-3.5、GPT-4等模型
- **通义千问配置**: 支持qwen-turbo、qwen-plus等模型
- **DeepSeek配置**: 支持deepseek-chat、deepseek-coder等模型
- **Gemini配置**: 支持gemini-1.5-flash、gemini-1.5-pro等模型
- **自定义API地址**: 支持私有部署的API

#### 生成参数调整
- **最大令牌数**: 控制生成文本的长度
- **创造性(Temperature)**: 控制生成内容的创新程度
- **自定义风格**: 可添加自己的动画风格和开头类型

## 配置文件说明

### config/settings.json
```json
{
    "app": {
        "name": "小说AI文案生成器",
        "version": "1.0.0",
        "window_size": [1200, 800]
    },
    "ai": {
        "default_provider": "openai",
        "providers": {
            "openai": {
                "api_key": "your-api-key",
                "model": "gpt-3.5-turbo",
                "base_url": "https://api.openai.com/v1"
            }
        }
    },
    "generation": {
        "max_tokens": 2000,
        "temperature": 0.8,
        "animation_styles": ["搞笑沙雕", "悬疑惊悚"],
        "opening_types": ["悬念开头", "对话开头"]
    }
}
```

## 常见问题

### Q: 如何获取AI API密钥？
A:
- **OpenAI**: 访问 https://platform.openai.com/ 注册并获取API密钥
- **通义千问**: 访问 https://dashscope.aliyuncs.com/ 注册并获取API密钥
- **DeepSeek**: 访问 https://platform.deepseek.com/ 注册并获取API密钥
- **Gemini**: 访问 https://aistudio.google.com/ 注册并获取API密钥

### Q: 生成失败怎么办？
A: 
1. 检查网络连接
2. 确认API密钥正确
3. 检查API余额是否充足
4. 查看日志文件 logs/app.log

### Q: 如何自定义动画风格？
A: 在设置中的"生成设置"标签页，可以添加自定义的动画风格和开头类型。

### Q: 支持哪些文件格式？
A: 目前支持UTF-8编码的txt文本文件，程序会自动检测文件编码。

## 技术架构

- **界面框架**: PySide6 (Qt6)
- **AI集成**: OpenAI API, 通义千问API
- **文本处理**: 正则表达式, chardet编码检测
- **配置管理**: JSON格式配置文件
- **日志系统**: Python logging模块

## 开发说明

### 项目结构
```
小说/
├── main.py              # 主程序入口
├── requirements.txt     # 依赖列表
├── config/             # 配置文件
│   └── settings.json
├── core/               # 核心功能
│   ├── ai_generator.py # AI生成器
│   └── text_processor.py # 文本处理
├── ui/                 # 界面文件
│   ├── main_window.py  # 主窗口
│   └── settings_dialog.py # 设置对话框
└── logs/               # 日志文件
```

### 扩展开发
- 添加新的AI提供商: 在 `core/ai_generator.py` 中扩展
- 自定义界面组件: 在 `ui/components/` 目录下添加
- 新增文本处理功能: 在 `core/text_processor.py` 中扩展

## 许可证

本项目仅供学习和个人使用。

## 更新日志

### v2.0.0 (2024-06-01)
- 新增DeepSeek和Gemini AI模型支持
- 新增智能分镜脚本生成功能
- 完善的视频制作工作流
- 优化用户界面和交互体验
- 增强错误处理和用户提示

### v1.0.0 (2024-01-01)
- 初始版本发布
- 支持OpenAI和通义千问API
- 基本的文案生成功能
- 图形化用户界面
