"""
主窗口界面
"""

import sys
import json
import logging
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QComboBox, QTabWidget,
    QFileDialog, QMessageBox, QProgressBar, QSplitter,
    QGroupBox, QGridLayout, QLineEdit, QSpinBox, QMenuBar,
    QMenu, QDialog
)
from PySide6.QtCore import Qt, QThread, QTimer, Signal
from PySide6.QtGui import QFont, QIcon, QAction

from core.ai_generator import AIGenerator
from core.text_processor import TextProcessor
from ui.settings_dialog import SettingsDialog

class GenerationWorker(QThread):
    """AI生成工作线程"""
    
    finished = Signal(str, str)  # 结果, 类型
    error = Signal(str)
    
    def __init__(self, generator, content, task_type, style_or_type):
        super().__init__()
        self.generator = generator
        self.content = content
        self.task_type = task_type
        self.style_or_type = style_or_type
    
    def run(self):
        try:
            if self.task_type == "animation":
                result = self.generator.generate_animation_script(self.content, self.style_or_type)
            elif self.task_type == "opening":
                result = self.generator.generate_opening(self.content, self.style_or_type)
            elif self.task_type == "storyboard":
                result = self.generator.generate_storyboard(self.content, self.style_or_type)
            else:
                result = "未知的生成类型"

            self.finished.emit(result, self.task_type)
        except Exception as e:
            self.error.emit(str(e))

class MainWindow(QMainWindow):
    """主窗口"""
    
    def __init__(self):
        super().__init__()
        self.ai_generator = AIGenerator()
        self.text_processor = TextProcessor()
        self.worker = None
        
        self.init_ui()
        self.load_settings()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("小说AI文案生成器 v1.0")
        self.setGeometry(100, 100, 1200, 800)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧输入区域
        left_widget = self.create_input_area()
        splitter.addWidget(left_widget)
        
        # 右侧输出区域
        right_widget = self.create_output_area()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([500, 700])
        
        # 状态栏
        self.statusBar().showMessage("就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)

        # 创建菜单栏
        self.create_menu_bar()

    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()

        # 文件菜单
        file_menu = menubar.addMenu("文件")

        open_action = QAction("打开文件", self)
        open_action.setShortcut("Ctrl+O")
        open_action.triggered.connect(self.open_file)
        file_menu.addAction(open_action)

        file_menu.addSeparator()

        exit_action = QAction("退出", self)
        exit_action.setShortcut("Ctrl+Q")
        exit_action.triggered.connect(self.close)
        file_menu.addAction(exit_action)

        # 工具菜单
        tools_menu = menubar.addMenu("工具")

        settings_action = QAction("设置", self)
        settings_action.triggered.connect(self.open_settings)
        tools_menu.addAction(settings_action)

        # 帮助菜单
        help_menu = menubar.addMenu("帮助")

        about_action = QAction("关于", self)
        about_action.triggered.connect(self.show_about)
        help_menu.addAction(about_action)

    def create_input_area(self):
        """创建输入区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 输入组
        input_group = QGroupBox("小说内容输入")
        input_layout = QVBoxLayout(input_group)
        
        # 文件操作按钮
        file_layout = QHBoxLayout()
        self.open_file_btn = QPushButton("打开文件")
        self.clear_btn = QPushButton("清空")
        file_layout.addWidget(self.open_file_btn)
        file_layout.addWidget(self.clear_btn)
        file_layout.addStretch()
        input_layout.addLayout(file_layout)
        
        # 文本输入框
        self.input_text = QTextEdit()
        self.input_text.setPlaceholderText("请输入小说内容，或点击'打开文件'导入...")
        self.input_text.setMinimumHeight(200)
        input_layout.addWidget(self.input_text)
        
        # 字数统计
        self.word_count_label = QLabel("字数: 0")
        input_layout.addWidget(self.word_count_label)
        
        layout.addWidget(input_group)
        
        # 生成设置组
        settings_group = QGroupBox("生成设置")
        settings_layout = QGridLayout(settings_group)
        
        # 动画风格
        settings_layout.addWidget(QLabel("动画风格:"), 0, 0)
        self.animation_style_combo = QComboBox()
        self.animation_style_combo.addItems(self.ai_generator.get_available_styles())
        settings_layout.addWidget(self.animation_style_combo, 0, 1)
        
        # 开头类型
        settings_layout.addWidget(QLabel("开头类型:"), 1, 0)
        self.opening_type_combo = QComboBox()
        self.opening_type_combo.addItems(self.ai_generator.get_available_opening_types())
        settings_layout.addWidget(self.opening_type_combo, 1, 1)
        
        layout.addWidget(settings_group)
        
        # 生成按钮
        button_layout = QHBoxLayout()
        self.generate_animation_btn = QPushButton("生成动画文案")
        self.generate_opening_btn = QPushButton("生成爆款开头")
        
        self.generate_animation_btn.setStyleSheet("""
            QPushButton {
                background-color: #4CAF50;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #45a049;
            }
        """)
        
        self.generate_opening_btn.setStyleSheet("""
            QPushButton {
                background-color: #2196F3;
                color: white;
                border: none;
                padding: 10px;
                font-size: 14px;
                border-radius: 5px;
            }
            QPushButton:hover {
                background-color: #1976D2;
            }
        """)
        
        button_layout.addWidget(self.generate_animation_btn)
        button_layout.addWidget(self.generate_opening_btn)
        layout.addLayout(button_layout)
        
        layout.addStretch()
        
        # 连接信号
        self.open_file_btn.clicked.connect(self.open_file)
        self.clear_btn.clicked.connect(self.clear_input)
        self.input_text.textChanged.connect(self.update_word_count)
        self.generate_animation_btn.clicked.connect(self.generate_animation)
        self.generate_opening_btn.clicked.connect(self.generate_opening)
        
        return widget
    
    def create_output_area(self):
        """创建输出区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 动画文案标签页
        animation_tab = QWidget()
        animation_layout = QVBoxLayout(animation_tab)
        
        self.animation_output = QTextEdit()
        self.animation_output.setPlaceholderText("生成的动画文案将显示在这里...")
        animation_layout.addWidget(self.animation_output)
        
        animation_buttons = QHBoxLayout()
        self.save_animation_btn = QPushButton("保存文案")
        self.copy_animation_btn = QPushButton("复制文案")
        animation_buttons.addWidget(self.save_animation_btn)
        animation_buttons.addWidget(self.copy_animation_btn)
        animation_buttons.addStretch()
        animation_layout.addLayout(animation_buttons)
        
        self.tab_widget.addTab(animation_tab, "动画文案")
        
        # 爆款开头标签页
        opening_tab = QWidget()
        opening_layout = QVBoxLayout(opening_tab)
        
        self.opening_output = QTextEdit()
        self.opening_output.setPlaceholderText("生成的爆款开头将显示在这里...")
        opening_layout.addWidget(self.opening_output)
        
        opening_buttons = QHBoxLayout()
        self.save_opening_btn = QPushButton("保存开头")
        self.copy_opening_btn = QPushButton("复制开头")
        opening_buttons.addWidget(self.save_opening_btn)
        opening_buttons.addWidget(self.copy_opening_btn)
        opening_buttons.addStretch()
        opening_layout.addLayout(opening_buttons)
        
        self.tab_widget.addTab(opening_tab, "爆款开头")

        # 分镜脚本标签页
        storyboard_tab = QWidget()
        storyboard_layout = QVBoxLayout(storyboard_tab)

        self.storyboard_output = QTextEdit()
        self.storyboard_output.setPlaceholderText("生成的分镜脚本将显示在这里...")
        storyboard_layout.addWidget(self.storyboard_output)

        storyboard_buttons = QHBoxLayout()
        self.save_storyboard_btn = QPushButton("保存分镜")
        self.copy_storyboard_btn = QPushButton("复制分镜")
        self.generate_storyboard_btn = QPushButton("生成分镜")
        self.generate_storyboard_btn.setStyleSheet("""
            QPushButton {
                background-color: #FF9800;
                color: white;
                border: none;
                padding: 8px;
                font-size: 12px;
                border-radius: 4px;
            }
            QPushButton:hover {
                background-color: #F57C00;
            }
        """)
        storyboard_buttons.addWidget(self.generate_storyboard_btn)
        storyboard_buttons.addWidget(self.save_storyboard_btn)
        storyboard_buttons.addWidget(self.copy_storyboard_btn)
        storyboard_buttons.addStretch()
        storyboard_layout.addLayout(storyboard_buttons)

        self.tab_widget.addTab(storyboard_tab, "分镜脚本")

        layout.addWidget(self.tab_widget)
        
        # 连接信号
        self.save_animation_btn.clicked.connect(lambda: self.save_output("animation"))
        self.copy_animation_btn.clicked.connect(lambda: self.copy_output("animation"))
        self.save_opening_btn.clicked.connect(lambda: self.save_output("opening"))
        self.copy_opening_btn.clicked.connect(lambda: self.copy_output("opening"))
        self.save_storyboard_btn.clicked.connect(lambda: self.save_output("storyboard"))
        self.copy_storyboard_btn.clicked.connect(lambda: self.copy_output("storyboard"))
        self.generate_storyboard_btn.clicked.connect(self.generate_storyboard)
        
        return widget

    def load_settings(self):
        """加载设置"""
        try:
            with open("config/settings.json", 'r', encoding='utf-8') as f:
                config = json.load(f)

            # 设置窗口大小
            window_size = config.get("app", {}).get("window_size", [1200, 800])
            self.resize(window_size[0], window_size[1])

        except Exception as e:
            logging.warning(f"加载设置失败: {e}")

    def open_file(self):
        """打开文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self,
            "选择小说文件",
            "",
            "文本文件 (*.txt);;所有文件 (*.*)"
        )

        if file_path:
            success, content = self.text_processor.read_file(file_path)
            if success:
                self.input_text.setPlainText(content)
                self.statusBar().showMessage(f"已加载文件: {Path(file_path).name}")
            else:
                QMessageBox.warning(self, "错误", content)

    def clear_input(self):
        """清空输入"""
        self.input_text.clear()
        self.statusBar().showMessage("已清空输入")

    def update_word_count(self):
        """更新字数统计"""
        text = self.input_text.toPlainText()
        word_count = self.text_processor.get_word_count(text)
        self.word_count_label.setText(f"字数: {word_count}")

    def generate_animation(self):
        """生成动画文案"""
        content = self.input_text.toPlainText()

        # 验证输入
        is_valid, error_msg = self.text_processor.validate_content(content)
        if not is_valid:
            QMessageBox.warning(self, "输入错误", error_msg)
            return

        # 获取选择的风格
        style = self.animation_style_combo.currentText()

        # 开始生成
        self.start_generation(content, "animation", style)

    def generate_opening(self):
        """生成爆款开头"""
        content = self.input_text.toPlainText()

        # 验证输入
        is_valid, error_msg = self.text_processor.validate_content(content)
        if not is_valid:
            QMessageBox.warning(self, "输入错误", error_msg)
            return

        # 获取选择的类型
        opening_type = self.opening_type_combo.currentText()

        # 开始生成
        self.start_generation(content, "opening", opening_type)

    def generate_storyboard(self):
        """生成分镜脚本"""
        # 检查是否有动画文案
        animation_content = self.animation_output.toPlainText()
        if not animation_content.strip():
            QMessageBox.warning(self, "输入错误", "请先生成动画文案，然后基于文案生成分镜脚本")
            return

        # 验证动画文案内容
        is_valid, error_msg = self.text_processor.validate_content(animation_content)
        if not is_valid:
            QMessageBox.warning(self, "内容错误", f"动画文案内容有误：{error_msg}")
            return

        # 开始生成分镜
        self.start_generation(animation_content, "storyboard", "动画分镜")

    def start_generation(self, content, task_type, style_or_type):
        """开始生成任务"""
        # 禁用按钮
        self.generate_animation_btn.setEnabled(False)
        self.generate_opening_btn.setEnabled(False)
        self.generate_storyboard_btn.setEnabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)  # 无限进度条

        # 更新状态
        task_names = {
            "animation": "动画文案",
            "opening": "爆款开头",
            "storyboard": "分镜脚本"
        }
        task_name = task_names.get(task_type, "内容")
        self.statusBar().showMessage(f"正在生成{task_name}...")

        # 创建工作线程
        self.worker = GenerationWorker(self.ai_generator, content, task_type, style_or_type)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.start()

    def on_generation_finished(self, result, task_type):
        """生成完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.generate_animation_btn.setEnabled(True)
        self.generate_opening_btn.setEnabled(True)
        self.generate_storyboard_btn.setEnabled(True)

        # 显示结果
        if task_type == "animation":
            self.animation_output.setPlainText(result)
            self.tab_widget.setCurrentIndex(0)  # 切换到动画文案标签页
            self.statusBar().showMessage("动画文案生成完成")
        elif task_type == "opening":
            self.opening_output.setPlainText(result)
            self.tab_widget.setCurrentIndex(1)  # 切换到爆款开头标签页
            self.statusBar().showMessage("爆款开头生成完成")
        elif task_type == "storyboard":
            self.storyboard_output.setPlainText(result)
            self.tab_widget.setCurrentIndex(2)  # 切换到分镜脚本标签页
            self.statusBar().showMessage("分镜脚本生成完成")

    def on_generation_error(self, error_msg):
        """生成错误"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.generate_animation_btn.setEnabled(True)
        self.generate_opening_btn.setEnabled(True)
        self.generate_storyboard_btn.setEnabled(True)

        # 显示错误
        QMessageBox.critical(self, "生成失败", f"生成过程中出现错误:\n{error_msg}")
        self.statusBar().showMessage("生成失败")

    def save_output(self, output_type):
        """保存输出"""
        if output_type == "animation":
            content = self.animation_output.toPlainText()
            default_name = "动画文案.txt"
        elif output_type == "opening":
            content = self.opening_output.toPlainText()
            default_name = "爆款开头.txt"
        elif output_type == "storyboard":
            content = self.storyboard_output.toPlainText()
            default_name = "分镜脚本.txt"
        else:
            content = ""
            default_name = "输出.txt"

        if not content.strip():
            QMessageBox.warning(self, "警告", "没有内容可保存")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self,
            "保存文件",
            default_name,
            "文本文件 (*.txt);;所有文件 (*.*)"
        )

        if file_path:
            success, error_msg = self.text_processor.save_to_file(content, file_path)
            if success:
                QMessageBox.information(self, "成功", "文件保存成功")
                self.statusBar().showMessage(f"已保存: {Path(file_path).name}")
            else:
                QMessageBox.critical(self, "错误", error_msg)

    def copy_output(self, output_type):
        """复制输出"""
        if output_type == "animation":
            content = self.animation_output.toPlainText()
        elif output_type == "opening":
            content = self.opening_output.toPlainText()
        elif output_type == "storyboard":
            content = self.storyboard_output.toPlainText()
        else:
            content = ""

        if not content.strip():
            QMessageBox.warning(self, "警告", "没有内容可复制")
            return

        # 复制到剪贴板
        clipboard = self.app.clipboard() if hasattr(self, 'app') else None
        if clipboard:
            clipboard.setText(content)
            self.statusBar().showMessage("已复制到剪贴板")
        else:
            QMessageBox.information(self, "提示", "请手动复制内容")

    def closeEvent(self, event):
        """关闭事件"""
        if self.worker and self.worker.isRunning():
            reply = QMessageBox.question(
                self,
                "确认退出",
                "正在生成中，确定要退出吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )

            if reply == QMessageBox.Yes:
                self.worker.terminate()
                self.worker.wait()
                event.accept()
            else:
                event.ignore()
        else:
            event.accept()

    def open_settings(self):
        """打开设置对话框"""
        dialog = SettingsDialog(self)
        if dialog.exec() == QDialog.Accepted:
            # 重新加载AI生成器配置
            self.ai_generator = AIGenerator()
            # 更新界面选项
            self.animation_style_combo.clear()
            self.animation_style_combo.addItems(self.ai_generator.get_available_styles())
            self.opening_type_combo.clear()
            self.opening_type_combo.addItems(self.ai_generator.get_available_opening_types())
            self.statusBar().showMessage("设置已更新")

    def show_about(self):
        """显示关于对话框"""
        QMessageBox.about(self, "关于",
            "小说AI文案生成器 v2.0\n\n"
            "基于Python 3.12和PySide6开发\n"
            "支持生成沙雕动画文案、爆款开头和分镜脚本\n\n"
            "功能特点：\n"
            "• 支持多种AI提供商 (OpenAI, 通义千问, DeepSeek, Gemini)\n"
            "• 多种动画风格选择\n"
            "• 多种开头类型生成\n"
            "• 智能分镜脚本生成\n"
            "• 文件导入导出\n"
            "• 实时字数统计\n\n"
            "新增功能：\n"
            "• DeepSeek和Gemini模型支持\n"
            "• 基于文案的分镜脚本生成\n"
            "• 完整的视频制作工作流"
        )
