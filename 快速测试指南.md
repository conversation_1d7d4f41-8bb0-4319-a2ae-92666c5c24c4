# 豆包图片导入功能 - 快速测试指南

## 🎯 测试目标

验证分镜图片生成器能够成功导入豆包生成的图片并保存到图片库。

## 🚀 测试步骤

### 测试1: 剪贴板导入功能

1. **准备测试图片**
   - 在网上找一张图片（任意图片即可）
   - 右键点击图片，选择"复制图片"

2. **测试导入**
   - 打开分镜图片生成器
   - 进入"豆包生图"标签页
   - 点击"从剪贴板导入"按钮

3. **验证结果**
   - 应该自动切换到"生成结果"标签页
   - 图片应该正确显示
   - 图片库中应该新增一条记录

### 测试2: 文件导入功能

1. **准备测试文件**
   - 保存一张图片到桌面（任意图片）

2. **测试导入**
   - 在"生成结果"标签页
   - 点击"导入图片"按钮
   - 选择刚才保存的图片文件

3. **验证结果**
   - 图片应该正确显示在结果区域
   - 图片库中应该新增记录

### 测试3: 粘贴图片功能

1. **复制图片**
   - 复制任意图片到剪贴板

2. **测试粘贴**
   - 在"生成结果"标签页
   - 点击"粘贴图片"按钮

3. **验证结果**
   - 图片应该直接显示在结果区域
   - 可以使用"保存图片"功能

### 测试4: 图片库功能

1. **查看图片库**
   - 切换到"图片库"标签页
   - 应该看到之前导入的所有图片

2. **测试查看功能**
   - 点击图片库中的任意项目
   - 应该自动切换到"生成结果"标签页并显示对应图片

3. **测试管理功能**
   - 尝试"清空图片库"功能
   - 尝试"导出全部"功能

## ✅ 预期结果

### 功能正常的标志
- ✅ 剪贴板导入成功，图片正确显示
- ✅ 文件导入成功，支持多种图片格式
- ✅ 粘贴功能正常，图片实时显示
- ✅ 图片库正确记录所有导入的图片
- ✅ 图片信息完整（提示词、风格、时间等）
- ✅ 保存功能正常，可以导出图片

### 界面表现
- ✅ 按钮响应正常，无卡顿
- ✅ 标签页切换流畅
- ✅ 图片显示清晰，缩放合适
- ✅ 状态栏显示操作反馈
- ✅ 错误提示友好明确

## 🔧 常见问题排查

### 问题1: 剪贴板导入提示"没有图片"
**原因**: 剪贴板中确实没有图片数据
**解决**: 确保右键选择"复制图片"而不是"复制图片地址"

### 问题2: 图片显示异常
**原因**: 图片格式不支持或文件损坏
**解决**: 尝试使用PNG或JPG格式的图片

### 问题3: 网页截图不工作
**原因**: WebEngine组件不可用
**解决**: 这是正常的，使用其他导入方式即可

### 问题4: 图片库记录丢失
**原因**: 临时文件被清理
**解决**: 使用"保存图片"功能将重要图片保存到永久位置

## 🎨 实际使用场景测试

### 场景1: 模拟豆包生图工作流

1. **输入提示词**
   - 在"当前场景信息"区域输入：
   - "一个古装美女推门而入，中景构图，古代房间背景"

2. **模拟豆包生图**
   - 找一张相关的古装美女图片
   - 复制到剪贴板

3. **导入和管理**
   - 使用"从剪贴板导入"功能
   - 查看生成结果和图片信息
   - 验证提示词是否正确关联

### 场景2: 批量图片管理

1. **导入多张图片**
   - 连续导入3-5张不同的图片
   - 使用不同的导入方式

2. **验证管理功能**
   - 查看图片库中的所有记录
   - 点击不同项目查看详情
   - 测试清空和导出功能

## 📊 测试检查清单

### 基础功能测试
- [ ] 剪贴板导入功能正常
- [ ] 文件导入功能正常
- [ ] 粘贴图片功能正常
- [ ] 网页截图功能（如果WebEngine可用）

### 图片库测试
- [ ] 图片自动添加到图片库
- [ ] 图片信息记录完整
- [ ] 点击查看功能正常
- [ ] 清空功能正常
- [ ] 导出功能正常

### 界面交互测试
- [ ] 按钮响应正常
- [ ] 标签页切换正常
- [ ] 图片显示正常
- [ ] 状态栏反馈正常
- [ ] 错误处理友好

### 数据持久化测试
- [ ] 图片保存功能正常
- [ ] 提示词关联正确
- [ ] 时间戳记录准确
- [ ] 文件路径有效

## 🎉 测试完成标准

当以下所有项目都通过时，表示功能测试成功：

### ✅ 核心功能
- 能够通过多种方式导入图片
- 图片正确显示在结果区域
- 图片库正确记录所有信息
- 保存和导出功能正常

### ✅ 用户体验
- 操作流程直观简单
- 错误提示清晰友好
- 界面响应流畅
- 功能说明详细

### ✅ 稳定性
- 无程序崩溃
- 无内存泄漏
- 错误恢复正常
- 兼容性良好

---

**开始测试**: 运行 `python main.py`，打开分镜图片生成器，按照上述步骤进行测试！

**测试重点**: 重点测试剪贴板导入功能，这是最常用的使用方式。
