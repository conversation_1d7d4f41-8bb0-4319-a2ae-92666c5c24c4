@echo off
chcp 65001 >nul
echo 启动小说AI文案生成器（WebEngine优化版）...
echo.

REM 设置WebEngine环境变量
echo 设置WebEngine环境变量...
set QT_OPENGL=software
set QTWEBENGINE_DISABLE_SANDBOX=1
set QTWEBENGINE_CHROMIUM_FLAGS=--disable-gpu --disable-gpu-sandbox --disable-software-rasterizer --disable-background-timer-throttling --disable-backgrounding-occluded-windows --disable-renderer-backgrounding --disable-features=TranslateUI --disable-ipc-flooding-protection --no-sandbox --single-process
set QT_LOGGING_RULES=qt.webenginecontext.debug=false

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.12+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖...
pip show PySide6 >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 检查WebEngine是否可用
echo 检查WebEngine组件...
python -c "from PySide6.QtWebEngineWidgets import QWebEngineView; print('WebEngine可用')" >nul 2>&1
if errorlevel 1 (
    echo 警告: WebEngine组件不可用，将使用备用方案
    echo 正在尝试安装WebEngine...
    pip install PySide6-Addons
)

REM 启动程序
echo 启动程序...
python main.py

if errorlevel 1 (
    echo.
    echo 程序异常退出，请检查日志文件 logs/app.log
    echo.
    echo 如果遇到WebEngine问题，请尝试以下解决方案：
    echo 1. 更新显卡驱动
    echo 2. 使用 run.bat 启动（不使用WebEngine）
    echo 3. 以管理员身份运行
    pause
)
