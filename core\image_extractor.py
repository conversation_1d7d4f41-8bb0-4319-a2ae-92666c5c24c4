"""
高级网页图片提取模块
支持多种图片提取方式：DOM解析、Canvas转换、Blob下载等
"""

import os
import base64
import logging
import tempfile
from datetime import datetime
from pathlib import Path
from PySide6.QtCore import QObject, Signal, QTimer
from PySide6.QtGui import QPixmap


class ImageExtractor(QObject):
    """高级图片提取器"""
    
    # 信号定义
    image_extracted = Signal(str, dict)  # 图片路径, 图片信息
    extraction_progress = Signal(int, str)  # 进度, 状态信息
    extraction_error = Signal(str)  # 错误信息
    
    def __init__(self, web_view=None):
        super().__init__()
        self.web_view = web_view
        self.extracted_images = []
        self.temp_dir = tempfile.gettempdir()
        
    def extract_images_from_page(self, extraction_type="all"):
        """
        从网页提取图片
        extraction_type: "all", "visible", "generated", "latest"
        """
        if not self.web_view:
            self.extraction_error.emit("WebEngine视图不可用")
            return
            
        try:
            if extraction_type == "all":
                self._extract_all_images()
            elif extraction_type == "visible":
                self._extract_visible_images()
            elif extraction_type == "generated":
                self._extract_generated_images()
            elif extraction_type == "latest":
                self._extract_latest_image()
            else:
                self.extraction_error.emit(f"不支持的提取类型: {extraction_type}")
                
        except Exception as e:
            logging.error(f"图片提取失败: {e}")
            self.extraction_error.emit(f"图片提取失败: {e}")
    
    def _extract_all_images(self):
        """提取页面所有图片"""
        self.extraction_progress.emit(10, "正在扫描页面图片...")
        
        # JavaScript代码：获取所有图片信息
        js_code = """
        (function() {
            var images = [];
            var imgElements = document.querySelectorAll('img');
            
            for (var i = 0; i < imgElements.length; i++) {
                var img = imgElements[i];
                if (img.src && img.complete && img.naturalWidth > 0) {
                    images.push({
                        src: img.src,
                        alt: img.alt || '',
                        width: img.naturalWidth,
                        height: img.naturalHeight,
                        visible: img.offsetWidth > 0 && img.offsetHeight > 0,
                        className: img.className || '',
                        id: img.id || '',
                        index: i
                    });
                }
            }
            
            return JSON.stringify(images);
        })();
        """
        
        self.web_view.page().runJavaScript(js_code, self._process_image_list)
    
    def _extract_visible_images(self):
        """提取可见图片"""
        self.extraction_progress.emit(10, "正在扫描可见图片...")
        
        js_code = """
        (function() {
            var images = [];
            var imgElements = document.querySelectorAll('img');
            
            for (var i = 0; i < imgElements.length; i++) {
                var img = imgElements[i];
                var rect = img.getBoundingClientRect();
                var isVisible = rect.width > 0 && rect.height > 0 && 
                               rect.top >= 0 && rect.left >= 0 &&
                               rect.bottom <= window.innerHeight && 
                               rect.right <= window.innerWidth;
                
                if (img.src && img.complete && img.naturalWidth > 0 && isVisible) {
                    images.push({
                        src: img.src,
                        alt: img.alt || '',
                        width: img.naturalWidth,
                        height: img.naturalHeight,
                        visible: true,
                        className: img.className || '',
                        id: img.id || '',
                        index: i
                    });
                }
            }
            
            return JSON.stringify(images);
        })();
        """
        
        self.web_view.page().runJavaScript(js_code, self._process_image_list)
    
    def _extract_generated_images(self):
        """提取AI生成的图片（基于特征识别）"""
        self.extraction_progress.emit(10, "正在识别AI生成图片...")
        
        js_code = """
        (function() {
            var images = [];
            var imgElements = document.querySelectorAll('img');
            
            // AI生成图片的常见特征
            var aiImagePatterns = [
                /generated/i,
                /ai[-_]?image/i,
                /dalle/i,
                /midjourney/i,
                /stable[-_]?diffusion/i,
                /doubao/i,
                /豆包/i,
                /generated[-_]?image/i,
                /ai[-_]?art/i
            ];
            
            for (var i = 0; i < imgElements.length; i++) {
                var img = imgElements[i];
                if (!img.src || !img.complete || img.naturalWidth <= 0) continue;
                
                var isAIGenerated = false;
                var imgSrc = img.src.toLowerCase();
                var imgAlt = (img.alt || '').toLowerCase();
                var imgClass = (img.className || '').toLowerCase();
                var imgId = (img.id || '').toLowerCase();
                
                // 检查是否匹配AI生成图片的特征
                for (var j = 0; j < aiImagePatterns.length; j++) {
                    if (aiImagePatterns[j].test(imgSrc) || 
                        aiImagePatterns[j].test(imgAlt) ||
                        aiImagePatterns[j].test(imgClass) ||
                        aiImagePatterns[j].test(imgId)) {
                        isAIGenerated = true;
                        break;
                    }
                }
                
                // 检查图片尺寸（AI生成图片通常有特定尺寸）
                var commonAISizes = [
                    {w: 512, h: 512}, {w: 1024, h: 1024}, {w: 768, h: 768},
                    {w: 1024, h: 768}, {w: 768, h: 1024}, {w: 1536, h: 1024}
                ];
                
                for (var k = 0; k < commonAISizes.length; k++) {
                    if (img.naturalWidth === commonAISizes[k].w && 
                        img.naturalHeight === commonAISizes[k].h) {
                        isAIGenerated = true;
                        break;
                    }
                }
                
                if (isAIGenerated) {
                    images.push({
                        src: img.src,
                        alt: img.alt || '',
                        width: img.naturalWidth,
                        height: img.naturalHeight,
                        visible: img.offsetWidth > 0 && img.offsetHeight > 0,
                        className: img.className || '',
                        id: img.id || '',
                        index: i,
                        aiGenerated: true
                    });
                }
            }
            
            return JSON.stringify(images);
        })();
        """
        
        self.web_view.page().runJavaScript(js_code, self._process_image_list)
    
    def _extract_latest_image(self):
        """提取最新添加的图片"""
        self.extraction_progress.emit(10, "正在查找最新图片...")
        
        js_code = """
        (function() {
            var images = [];
            var imgElements = document.querySelectorAll('img');
            var latestImg = null;
            var latestTime = 0;
            
            for (var i = 0; i < imgElements.length; i++) {
                var img = imgElements[i];
                if (!img.src || !img.complete || img.naturalWidth <= 0) continue;
                
                // 尝试获取图片的时间戳（如果有的话）
                var timestamp = Date.now(); // 默认使用当前时间
                
                // 检查data属性中的时间戳
                if (img.dataset.timestamp) {
                    timestamp = parseInt(img.dataset.timestamp);
                } else if (img.dataset.created) {
                    timestamp = parseInt(img.dataset.created);
                }
                
                if (timestamp >= latestTime) {
                    latestTime = timestamp;
                    latestImg = img;
                }
            }
            
            if (latestImg) {
                images.push({
                    src: latestImg.src,
                    alt: latestImg.alt || '',
                    width: latestImg.naturalWidth,
                    height: latestImg.naturalHeight,
                    visible: latestImg.offsetWidth > 0 && latestImg.offsetHeight > 0,
                    className: latestImg.className || '',
                    id: latestImg.id || '',
                    index: Array.from(imgElements).indexOf(latestImg),
                    timestamp: latestTime,
                    isLatest: true
                });
            }
            
            return JSON.stringify(images);
        })();
        """
        
        self.web_view.page().runJavaScript(js_code, self._process_image_list)
    
    def _process_image_list(self, result):
        """处理JavaScript返回的图片列表"""
        try:
            if not result:
                self.extraction_error.emit("未找到任何图片")
                return
                
            import json
            images = json.loads(result)
            
            if not images:
                self.extraction_error.emit("页面中没有找到符合条件的图片")
                return
                
            self.extraction_progress.emit(30, f"找到 {len(images)} 张图片，开始下载...")
            
            # 逐个处理图片
            self.current_images = images
            self.current_index = 0
            self._process_next_image()
            
        except Exception as e:
            logging.error(f"处理图片列表失败: {e}")
            self.extraction_error.emit(f"处理图片列表失败: {e}")
    
    def _process_next_image(self):
        """处理下一张图片"""
        if self.current_index >= len(self.current_images):
            self.extraction_progress.emit(100, "所有图片提取完成")
            return
            
        image_info = self.current_images[self.current_index]
        progress = int((self.current_index + 1) / len(self.current_images) * 70) + 30
        
        self.extraction_progress.emit(
            progress, 
            f"正在处理第 {self.current_index + 1}/{len(self.current_images)} 张图片..."
        )
        
        # 提取单张图片
        self._extract_single_image(image_info)
        
        self.current_index += 1
        
        # 延迟处理下一张图片，避免过快请求
        QTimer.singleShot(100, self._process_next_image)
    
    def _extract_single_image(self, image_info):
        """提取单张图片"""
        try:
            src = image_info.get('src', '')

            if src.startswith('data:image'):
                # Base64图片直接保存
                self._save_base64_image(src, image_info)
            elif src.startswith('blob:'):
                # Blob URL需要特殊处理
                self._extract_blob_image(src, image_info)
            elif src.startswith('http'):
                # 网络图片优先尝试直接下载
                self._download_network_image(src, image_info)
            else:
                logging.warning(f"不支持的图片源: {src}")

        except Exception as e:
            logging.error(f"提取单张图片失败: {e}")
    
    def _save_base64_image(self, data_url, image_info):
        """保存Base64图片"""
        try:
            # 解析data URL
            header, data = data_url.split(',', 1)
            image_data = base64.b64decode(data)
            
            # 确定文件扩展名
            if 'png' in header:
                ext = '.png'
            elif 'jpeg' in header or 'jpg' in header:
                ext = '.jpg'
            elif 'gif' in header:
                ext = '.gif'
            elif 'webp' in header:
                ext = '.webp'
            else:
                ext = '.png'  # 默认
            
            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"extracted_image_{timestamp}{ext}"
            filepath = os.path.join(self.temp_dir, filename)
            
            # 保存文件
            with open(filepath, 'wb') as f:
                f.write(image_data)
            
            # 验证图片
            pixmap = QPixmap(filepath)
            if not pixmap.isNull():
                # 更新图片信息
                image_info.update({
                    'local_path': filepath,
                    'file_size': len(image_data),
                    'format': ext[1:],
                    'extraction_method': 'base64'
                })
                
                self.image_extracted.emit(filepath, image_info)
                logging.info(f"Base64图片保存成功: {filepath}")
            else:
                logging.error(f"保存的图片文件无效: {filepath}")
                
        except Exception as e:
            logging.error(f"保存Base64图片失败: {e}")
    
    def _extract_blob_image(self, blob_url, image_info):
        """提取Blob URL图片"""
        js_code = f"""
        (function() {{
            var img = new Image();
            img.crossOrigin = 'anonymous';
            img.onload = function() {{
                try {{
                    var canvas = document.createElement('canvas');
                    canvas.width = img.naturalWidth;
                    canvas.height = img.naturalHeight;
                    var ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);
                    var dataURL = canvas.toDataURL('image/png');
                    window.qt_extracted_image = dataURL;
                }} catch(e) {{
                    window.qt_extracted_image = 'ERROR: ' + e.message;
                }}
            }};
            img.onerror = function() {{
                window.qt_extracted_image = 'ERROR: Failed to load image';
            }};
            img.src = '{blob_url}';
            
            // 等待图片加载
            setTimeout(function() {{
                if (window.qt_extracted_image) {{
                    return window.qt_extracted_image;
                }} else {{
                    return 'ERROR: Timeout';
                }}
            }}, 3000);
        }})();
        """
        
        def handle_blob_result(result):
            if result and result.startswith('data:image'):
                image_info['extraction_method'] = 'blob_canvas'
                self._save_base64_image(result, image_info)
            else:
                logging.error(f"Blob图片提取失败: {result}")
        
        self.web_view.page().runJavaScript(js_code, handle_blob_result)
    
    def _convert_network_image_to_base64(self, img_url, image_info):
        """将网络图片转换为Base64"""
        # 首先尝试JavaScript Canvas方法
        js_code = f"""
        (function() {{
            return new Promise(function(resolve) {{
                var img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = function() {{
                    try {{
                        var canvas = document.createElement('canvas');
                        canvas.width = img.naturalWidth;
                        canvas.height = img.naturalHeight;
                        var ctx = canvas.getContext('2d');
                        ctx.drawImage(img, 0, 0);
                        var dataURL = canvas.toDataURL('image/png');
                        resolve(dataURL);
                    }} catch(e) {{
                        resolve('ERROR: ' + e.message);
                    }}
                }};
                img.onerror = function() {{
                    resolve('ERROR: Failed to load image');
                }};
                img.src = '{img_url}';

                // 设置超时
                setTimeout(function() {{
                    resolve('ERROR: Timeout');
                }}, 5000);
            }});
        }})();
        """

        def handle_network_result(result):
            if result and result.startswith('data:image'):
                image_info['extraction_method'] = 'network_canvas'
                self._save_base64_image(result, image_info)
            else:
                # Canvas方法失败，尝试直接下载
                logging.warning(f"Canvas方法失败: {result}，尝试直接下载")
                self._download_network_image(img_url, image_info)

        self.web_view.page().runJavaScript(js_code, handle_network_result)

    def _download_network_image(self, img_url, image_info):
        """直接下载网络图片"""
        try:
            import requests
            import tempfile
            from datetime import datetime
            from urllib.parse import urlparse

            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
                'Referer': 'https://www.doubao.com/'
            }

            # 下载图片
            response = requests.get(img_url, headers=headers, timeout=10)
            response.raise_for_status()

            # 确定文件扩展名
            content_type = response.headers.get('content-type', '')
            if 'png' in content_type:
                ext = '.png'
            elif 'jpeg' in content_type or 'jpg' in content_type:
                ext = '.jpg'
            elif 'gif' in content_type:
                ext = '.gif'
            elif 'webp' in content_type:
                ext = '.webp'
            else:
                # 从URL推断
                parsed_url = urlparse(img_url)
                path = parsed_url.path.lower()
                if path.endswith('.png'):
                    ext = '.png'
                elif path.endswith(('.jpg', '.jpeg')):
                    ext = '.jpg'
                elif path.endswith('.gif'):
                    ext = '.gif'
                elif path.endswith('.webp'):
                    ext = '.webp'
                else:
                    ext = '.png'  # 默认

            # 生成文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            filename = f"extracted_image_{timestamp}{ext}"
            filepath = os.path.join(self.temp_dir, filename)

            # 保存文件
            with open(filepath, 'wb') as f:
                f.write(response.content)

            # 验证图片
            from PySide6.QtGui import QPixmap
            pixmap = QPixmap(filepath)
            if not pixmap.isNull():
                # 更新图片信息
                image_info.update({
                    'local_path': filepath,
                    'file_size': len(response.content),
                    'format': ext[1:],
                    'extraction_method': 'network_download'
                })

                self.image_extracted.emit(filepath, image_info)
                logging.info(f"网络图片下载成功: {filepath}")
            else:
                logging.error(f"下载的图片文件无效: {filepath}")
                # 最后备用方案：返回原始URL
                image_info.update({
                    'local_path': img_url,
                    'extraction_method': 'url_fallback',
                    'note': '下载失败，返回原始URL'
                })
                self.image_extracted.emit(img_url, image_info)

        except Exception as e:
            logging.error(f"下载网络图片失败: {e}")
            # 最后备用方案：返回原始URL
            image_info.update({
                'local_path': img_url,
                'extraction_method': 'url_fallback',
                'note': f'下载失败: {e}'
            })
            self.image_extracted.emit(img_url, image_info)
