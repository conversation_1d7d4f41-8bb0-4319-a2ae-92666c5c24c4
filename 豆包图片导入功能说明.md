# 豆包图片导入功能使用说明

## 🎯 功能概述

现在分镜图片生成器支持多种方式导入豆包生成的图片，并自动保存到图片库中。无论WebEngine是否可用，都能完美使用豆包生图功能。

## 🚀 功能特色

### ✅ 多种导入方式
- **网页截图** - 直接截取豆包网页内容（WebEngine可用时）
- **文件导入** - 选择本地保存的图片文件
- **剪贴板导入** - 从剪贴板导入复制的图片
- **粘贴显示** - 直接粘贴图片到结果显示区域

### ✅ 完整的图片管理
- **自动保存** - 导入的图片自动添加到图片库
- **详细信息** - 记录提示词、风格、时间等元数据
- **快速查看** - 点击图片库项目快速查看详情
- **批量管理** - 支持清空、导出等操作

## 📋 详细使用方法

### 方法1: 网页截图（推荐 - WebEngine可用时）

1. **在豆包生图标签页**
   - 使用内嵌的豆包网页生成图片
   - 等待图片生成完成

2. **截图保存**
   - 点击"捕获图片"按钮
   - 系统自动截取网页内容
   - 图片自动保存并添加到图片库

3. **查看结果**
   - 自动切换到"生成结果"标签页
   - 显示截图内容和详细信息

### 方法2: 剪贴板导入（最便捷）

1. **复制豆包生成的图片**
   - 在豆包网页中右键点击生成的图片
   - 选择"复制图片"

2. **导入到应用**
   - 在豆包生图标签页点击"从剪贴板导入"
   - 或在生成结果标签页点击"粘贴图片"

3. **自动处理**
   - 图片自动显示在结果区域
   - 自动添加到图片库

### 方法3: 文件导入（最稳定）

1. **保存豆包图片**
   - 在豆包网页中右键点击生成的图片
   - 选择"图片另存为"保存到本地

2. **导入文件**
   - 在生成结果标签页点击"导入图片"
   - 选择刚才保存的图片文件

3. **完成导入**
   - 图片显示在结果区域
   - 自动添加到图片库

### 方法4: 外部浏览器使用（WebEngine不可用时）

1. **打开外部浏览器**
   - 点击"刷新"或"返回首页"按钮
   - 在打开的浏览器中使用豆包生图

2. **保存图片**
   - 右键保存豆包生成的图片
   - 或复制图片到剪贴板

3. **导入应用**
   - 使用"导入图片"或"从剪贴板导入"功能

## 🎨 界面功能详解

### 豆包生图标签页

#### 控制按钮
- **刷新** - 刷新豆包网页或打开外部浏览器
- **返回首页** - 返回豆包首页
- **捕获图片** - 截取网页内容（WebEngine可用时）
- **从剪贴板导入** - 导入复制的图片

#### 网页区域
- **内嵌网页** - 直接显示豆包网站（WebEngine可用时）
- **使用说明** - 详细的操作指南（WebEngine不可用时）

### 生成结果标签页

#### 控制按钮
- **保存图片** - 保存当前显示的图片
- **复制提示词** - 复制当前的提示词
- **导入图片** - 从文件导入图片
- **粘贴图片** - 从剪贴板粘贴图片

#### 显示区域
- **图片显示** - 显示当前图片或链接信息
- **图片信息** - 显示风格、时间、提示词等详细信息

### 图片库标签页

#### 功能特色
- **完整记录** - 所有导入的图片都有详细记录
- **分类显示** - 区分网页截图、导入图片、剪贴板图片等
- **快速查看** - 点击任意项目快速查看详情
- **批量操作** - 清空、导出等批量管理功能

## 💡 使用技巧

### 提高效率的方法

1. **快捷键操作**
   - 复制图片：Ctrl+C
   - 粘贴图片：使用"粘贴图片"按钮

2. **批量处理**
   - 生成多张图片后，逐个复制粘贴
   - 使用图片库统一管理

3. **提示词管理**
   - 在生成图片前先输入提示词
   - 导入的图片会自动关联当前提示词

### 最佳实践

1. **推荐工作流程**
   ```
   输入提示词 → 豆包生图 → 复制图片 → 从剪贴板导入 → 查看结果
   ```

2. **质量保证**
   - 使用高质量的提示词
   - 选择合适的图片风格
   - 保存重要的生成结果

3. **文件管理**
   - 定期导出图片库
   - 备份重要的图片文件
   - 清理临时文件

## 🔧 故障排除

### 常见问题

#### 1. 剪贴板导入失败
- **原因**: 剪贴板中没有图片数据
- **解决**: 确保右键选择"复制图片"而不是"复制图片地址"

#### 2. 网页截图不工作
- **原因**: WebEngine组件不可用
- **解决**: 使用剪贴板导入或文件导入方式

#### 3. 图片显示异常
- **原因**: 图片文件损坏或格式不支持
- **解决**: 重新保存图片，确保使用支持的格式

#### 4. 图片库记录丢失
- **原因**: 程序重启后临时文件清理
- **解决**: 使用"保存图片"功能将重要图片保存到永久位置

### 支持的图片格式
- PNG（推荐）
- JPG/JPEG
- BMP
- GIF
- WebP

## 📊 功能对比

| 导入方式 | WebEngine要求 | 操作便捷性 | 图片质量 | 推荐指数 |
|---------|--------------|-----------|---------|---------|
| 网页截图 | 需要 | ⭐⭐⭐ | ⭐⭐⭐ | ⭐⭐⭐⭐ |
| 剪贴板导入 | 不需要 | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ |
| 文件导入 | 不需要 | ⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |
| 粘贴显示 | 不需要 | ⭐⭐⭐⭐ | ⭐⭐⭐⭐⭐ | ⭐⭐⭐⭐ |

## 🎉 总结

现在您可以通过多种方式将豆包生成的图片导入到分镜图片生成器中：

### ✅ 已实现功能
- 🖼️ **多种导入方式** - 网页截图、文件导入、剪贴板导入
- 📱 **完整图片管理** - 自动保存、详细信息、批量操作
- 🔄 **无缝工作流** - 从生成到保存的完整流程
- 🛡️ **兼容性保证** - WebEngine可用或不可用都能正常使用

### 🚀 推荐使用方式
1. **最便捷**: 剪贴板导入 - 复制图片后直接导入
2. **最稳定**: 文件导入 - 保存文件后选择导入
3. **最直接**: 网页截图 - 直接截取网页内容（需WebEngine）

无论您的系统环境如何，都能完美使用豆包生图功能并管理生成的图片！

---

**提示**: 建议优先使用"剪贴板导入"方式，操作最简单，图片质量最好！
