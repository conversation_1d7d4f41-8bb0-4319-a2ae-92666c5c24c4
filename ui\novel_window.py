"""
小说创作窗口
"""

import json
import logging
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QComboBox, QTabWidget,
    QFileDialog, QMessageBox, QProgressBar, QSplitter,
    QGroupBox, QGridLayout, QLineEdit, QSpinBox, QListWidget,
    QListWidgetItem, QDialog, QDialogButtonBox, QFormLayout
)
from PySide6.QtCore import Qt, QThread, Signal
from PySide6.QtGui import QFont, QAction

from core.novel_creator import NovelCreator
from core.ai_generator import AIGenerator
from core.text_processor import TextProcessor

class NovelGenerationWorker(QThread):
    """小说生成工作线程"""
    
    finished = Signal(str, str)  # 结果, 类型
    error = Signal(str)
    
    def __init__(self, novel_creator, task_type, **kwargs):
        super().__init__()
        self.novel_creator = novel_creator
        self.task_type = task_type
        self.kwargs = kwargs
    
    def run(self):
        try:
            if self.task_type == "outline":
                result = self.novel_creator.generate_story_outline(
                    self.kwargs['theme'], 
                    self.kwargs['genre'], 
                    self.kwargs['length']
                )
            elif self.task_type == "chapters":
                result = self.novel_creator.generate_chapter_plan(
                    self.kwargs['outline'], 
                    self.kwargs['total_chapters']
                )
            elif self.task_type == "continue":
                result = self.novel_creator.continue_chapter(
                    self.kwargs['previous_content'],
                    self.kwargs['chapter_outline'],
                    self.kwargs['target_length']
                )
            elif self.task_type == "character":
                result = self.novel_creator.generate_character_profile(
                    self.kwargs['character_name'],
                    self.kwargs['role'],
                    self.kwargs['story_context']
                )
            elif self.task_type == "world":
                result = self.novel_creator.generate_world_building(
                    self.kwargs['genre'],
                    self.kwargs['setting']
                )
            elif self.task_type == "optimize":
                result = self.novel_creator.optimize_chapter(
                    self.kwargs['chapter_content'],
                    self.kwargs['optimization_focus']
                )
            else:
                result = "未知的生成类型"
            
            self.finished.emit(result, self.task_type)
        except Exception as e:
            self.error.emit(str(e))

class ChapterDialog(QDialog):
    """章节编辑对话框"""
    
    def __init__(self, parent=None, chapter_title="", chapter_content=""):
        super().__init__(parent)
        self.setWindowTitle("章节编辑")
        self.setModal(True)
        self.resize(800, 600)
        
        layout = QVBoxLayout(self)
        
        # 章节标题
        title_layout = QHBoxLayout()
        title_layout.addWidget(QLabel("章节标题:"))
        self.title_edit = QLineEdit(chapter_title)
        title_layout.addWidget(self.title_edit)
        layout.addLayout(title_layout)
        
        # 章节内容
        layout.addWidget(QLabel("章节内容:"))
        self.content_edit = QTextEdit(chapter_content)
        layout.addWidget(self.content_edit)
        
        # 按钮
        buttons = QDialogButtonBox(QDialogButtonBox.Ok | QDialogButtonBox.Cancel)
        buttons.accepted.connect(self.accept)
        buttons.rejected.connect(self.reject)
        layout.addWidget(buttons)
    
    def get_data(self):
        return self.title_edit.text(), self.content_edit.toPlainText()

class NovelWindow(QMainWindow):
    """小说创作主窗口"""
    
    def __init__(self):
        super().__init__()
        self.ai_generator = AIGenerator()
        self.novel_creator = NovelCreator(self.ai_generator)
        self.text_processor = TextProcessor()
        self.worker = None
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("小说创作工作室")
        self.setGeometry(100, 100, 1400, 900)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制区域
        left_widget = self.create_control_area()
        splitter.addWidget(left_widget)
        
        # 右侧工作区域
        right_widget = self.create_work_area()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([400, 1000])
        
        # 状态栏
        self.statusBar().showMessage("小说创作工作室已就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
        
        # 创建菜单栏
        self.create_menu_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        new_action = QAction("新建项目", self)
        new_action.triggered.connect(self.new_project)
        file_menu.addAction(new_action)
        
        open_action = QAction("打开项目", self)
        open_action.triggered.connect(self.open_project)
        file_menu.addAction(open_action)
        
        save_action = QAction("保存项目", self)
        save_action.triggered.connect(self.save_project)
        file_menu.addAction(save_action)
        
        file_menu.addSeparator()
        
        export_action = QAction("导出小说", self)
        export_action.triggered.connect(self.export_novel)
        file_menu.addAction(export_action)
    
    def create_control_area(self):
        """创建控制区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 项目信息组
        project_group = QGroupBox("项目信息")
        project_layout = QGridLayout(project_group)
        
        project_layout.addWidget(QLabel("小说标题:"), 0, 0)
        self.novel_title = QLineEdit()
        project_layout.addWidget(self.novel_title, 0, 1)
        
        project_layout.addWidget(QLabel("作者:"), 1, 0)
        self.author_name = QLineEdit()
        project_layout.addWidget(self.author_name, 1, 1)
        
        project_layout.addWidget(QLabel("类型:"), 2, 0)
        self.genre_combo = QComboBox()
        self.genre_combo.addItems([
            "玄幻", "都市", "历史", "科幻", "悬疑", 
            "言情", "武侠", "军事", "游戏", "其他"
        ])
        project_layout.addWidget(self.genre_combo, 2, 1)
        
        layout.addWidget(project_group)
        
        # 创作工具组
        tools_group = QGroupBox("创作工具")
        tools_layout = QVBoxLayout(tools_group)
        
        # 故事大纲
        outline_layout = QHBoxLayout()
        self.theme_input = QLineEdit()
        self.theme_input.setPlaceholderText("输入故事主题...")
        outline_layout.addWidget(self.theme_input)
        
        self.generate_outline_btn = QPushButton("生成大纲")
        self.generate_outline_btn.clicked.connect(self.generate_outline)
        outline_layout.addWidget(self.generate_outline_btn)
        tools_layout.addLayout(outline_layout)
        
        # 章节规划
        chapter_layout = QHBoxLayout()
        chapter_layout.addWidget(QLabel("章节数:"))
        self.chapter_count = QSpinBox()
        self.chapter_count.setRange(5, 100)
        self.chapter_count.setValue(20)
        chapter_layout.addWidget(self.chapter_count)
        
        self.generate_chapters_btn = QPushButton("生成章节")
        self.generate_chapters_btn.clicked.connect(self.generate_chapters)
        chapter_layout.addWidget(self.generate_chapters_btn)
        tools_layout.addLayout(chapter_layout)
        
        # 其他工具
        self.generate_character_btn = QPushButton("角色设定")
        self.generate_character_btn.clicked.connect(self.generate_character)
        tools_layout.addWidget(self.generate_character_btn)
        
        self.generate_world_btn = QPushButton("世界观设定")
        self.generate_world_btn.clicked.connect(self.generate_world)
        tools_layout.addWidget(self.generate_world_btn)
        
        layout.addWidget(tools_group)
        
        # 章节列表
        chapters_group = QGroupBox("章节列表")
        chapters_layout = QVBoxLayout(chapters_group)
        
        self.chapter_list = QListWidget()
        self.chapter_list.itemDoubleClicked.connect(self.edit_chapter)
        chapters_layout.addWidget(self.chapter_list)
        
        chapter_buttons = QHBoxLayout()
        self.add_chapter_btn = QPushButton("添加章节")
        self.add_chapter_btn.clicked.connect(self.add_chapter)
        chapter_buttons.addWidget(self.add_chapter_btn)
        
        self.delete_chapter_btn = QPushButton("删除章节")
        self.delete_chapter_btn.clicked.connect(self.delete_chapter)
        chapter_buttons.addWidget(self.delete_chapter_btn)
        chapters_layout.addLayout(chapter_buttons)
        
        layout.addWidget(chapters_group)
        
        layout.addStretch()
        
        return widget

    def create_work_area(self):
        """创建工作区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)

        # 标签页
        self.tab_widget = QTabWidget()

        # 故事大纲标签页
        outline_tab = QWidget()
        outline_layout = QVBoxLayout(outline_tab)

        self.outline_output = QTextEdit()
        self.outline_output.setPlaceholderText("故事大纲将显示在这里...")
        outline_layout.addWidget(self.outline_output)

        outline_buttons = QHBoxLayout()
        self.save_outline_btn = QPushButton("保存大纲")
        self.save_outline_btn.clicked.connect(lambda: self.save_content("outline"))
        outline_buttons.addWidget(self.save_outline_btn)
        outline_buttons.addStretch()
        outline_layout.addLayout(outline_buttons)

        self.tab_widget.addTab(outline_tab, "故事大纲")

        # 章节规划标签页
        chapters_tab = QWidget()
        chapters_layout = QVBoxLayout(chapters_tab)

        self.chapters_output = QTextEdit()
        self.chapters_output.setPlaceholderText("章节规划将显示在这里...")
        chapters_layout.addWidget(self.chapters_output)

        chapters_buttons = QHBoxLayout()
        self.save_chapters_btn = QPushButton("保存规划")
        self.save_chapters_btn.clicked.connect(lambda: self.save_content("chapters"))
        chapters_buttons.addWidget(self.save_chapters_btn)
        chapters_buttons.addStretch()
        chapters_layout.addLayout(chapters_buttons)

        self.tab_widget.addTab(chapters_tab, "章节规划")

        # 章节编写标签页
        writing_tab = QWidget()
        writing_layout = QVBoxLayout(writing_tab)

        # 章节选择区域
        chapter_select_layout = QHBoxLayout()
        chapter_select_layout.addWidget(QLabel("选择章节:"))

        self.chapter_selector = QComboBox()
        self.chapter_selector.setMinimumWidth(200)
        self.chapter_selector.currentTextChanged.connect(self.on_chapter_selected)
        chapter_select_layout.addWidget(self.chapter_selector)

        chapter_select_layout.addStretch()
        writing_layout.addLayout(chapter_select_layout)

        # 当前章节信息
        current_chapter_layout = QHBoxLayout()
        current_chapter_layout.addWidget(QLabel("当前章节:"))
        self.current_chapter_label = QLabel("未选择")
        current_chapter_layout.addWidget(self.current_chapter_label)
        current_chapter_layout.addStretch()

        self.continue_writing_btn = QPushButton("续写章节")
        self.continue_writing_btn.clicked.connect(self.continue_writing)
        current_chapter_layout.addWidget(self.continue_writing_btn)

        self.optimize_chapter_btn = QPushButton("优化章节")
        self.optimize_chapter_btn.clicked.connect(self.optimize_chapter)
        current_chapter_layout.addWidget(self.optimize_chapter_btn)

        self.save_chapter_btn = QPushButton("保存章节")
        self.save_chapter_btn.clicked.connect(self.save_current_chapter)
        current_chapter_layout.addWidget(self.save_chapter_btn)

        writing_layout.addLayout(current_chapter_layout)

        # 章节内容编辑
        self.chapter_content = QTextEdit()
        self.chapter_content.setPlaceholderText("在这里编写章节内容...")
        writing_layout.addWidget(self.chapter_content)

        # 字数统计
        stats_layout = QHBoxLayout()
        self.word_count_label = QLabel("字数: 0")
        stats_layout.addWidget(self.word_count_label)
        stats_layout.addStretch()
        writing_layout.addLayout(stats_layout)

        self.tab_widget.addTab(writing_tab, "章节编写")

        # 角色设定标签页
        character_tab = QWidget()
        character_layout = QVBoxLayout(character_tab)

        # 主角设定区域
        protagonist_group = QGroupBox("主角设定")
        protagonist_layout = QGridLayout(protagonist_group)

        protagonist_layout.addWidget(QLabel("主角姓名:"), 0, 0)
        self.protagonist_name = QLineEdit()
        self.protagonist_name.setPlaceholderText("请输入主角姓名...")
        self.protagonist_name.textChanged.connect(self.on_project_data_changed)
        protagonist_layout.addWidget(self.protagonist_name, 0, 1)

        protagonist_layout.addWidget(QLabel("主角性别:"), 0, 2)
        self.protagonist_gender = QComboBox()
        self.protagonist_gender.addItems(["男", "女", "其他"])
        self.protagonist_gender.currentTextChanged.connect(self.on_project_data_changed)
        protagonist_layout.addWidget(self.protagonist_gender, 0, 3)

        protagonist_layout.addWidget(QLabel("主角年龄:"), 1, 0)
        self.protagonist_age = QLineEdit()
        self.protagonist_age.setPlaceholderText("如：25岁")
        self.protagonist_age.textChanged.connect(self.on_project_data_changed)
        protagonist_layout.addWidget(self.protagonist_age, 1, 1)

        protagonist_layout.addWidget(QLabel("主角职业:"), 1, 2)
        self.protagonist_occupation = QLineEdit()
        self.protagonist_occupation.setPlaceholderText("如：学生、程序员等")
        self.protagonist_occupation.textChanged.connect(self.on_project_data_changed)
        protagonist_layout.addWidget(self.protagonist_occupation, 1, 3)

        # 生成角色设定按钮
        generate_char_layout = QHBoxLayout()
        generate_char_layout.addStretch()
        self.generate_character_btn = QPushButton("生成角色设定")
        self.generate_character_btn.clicked.connect(self.generate_character)
        generate_char_layout.addWidget(self.generate_character_btn)

        protagonist_layout.addLayout(generate_char_layout, 2, 0, 1, 4)
        character_layout.addWidget(protagonist_group)

        # 角色设定输出区域
        self.character_output = QTextEdit()
        self.character_output.setPlaceholderText("角色设定将显示在这里...")
        self.character_output.textChanged.connect(self.on_project_data_changed)
        character_layout.addWidget(self.character_output)

        self.tab_widget.addTab(character_tab, "角色设定")

        # 世界观标签页
        world_tab = QWidget()
        world_layout = QVBoxLayout(world_tab)

        self.world_output = QTextEdit()
        self.world_output.setPlaceholderText("世界观设定将显示在这里...")
        world_layout.addWidget(self.world_output)

        self.tab_widget.addTab(world_tab, "世界观")

        layout.addWidget(self.tab_widget)

        # 连接信号
        self.chapter_content.textChanged.connect(self.update_word_count)
        self.chapter_content.textChanged.connect(self.on_content_changed)

        # 连接项目数据变化信号
        self.novel_title.textChanged.connect(self.on_project_data_changed)
        self.author_name.textChanged.connect(self.on_project_data_changed)
        self.theme_input.textChanged.connect(self.on_project_data_changed)
        self.genre_combo.currentTextChanged.connect(self.on_project_data_changed)
        self.chapter_count.valueChanged.connect(self.on_project_data_changed)
        self.outline_output.textChanged.connect(self.on_project_data_changed)
        self.chapters_output.textChanged.connect(self.on_project_data_changed)
        self.world_output.textChanged.connect(self.on_project_data_changed)

        # 自动保存定时器
        from PySide6.QtCore import QTimer
        self.auto_save_timer = QTimer()
        self.auto_save_timer.setSingleShot(True)
        self.auto_save_timer.timeout.connect(self.auto_save_current_chapter)
        self.auto_save_delay = 2000  # 2秒延迟自动保存

        # 项目数据自动保存定时器
        self.project_save_timer = QTimer()
        self.project_save_timer.setSingleShot(True)
        self.project_save_timer.timeout.connect(self.auto_save_project_data)
        self.project_save_delay = 3000  # 3秒延迟自动保存项目数据

        # 加载上次的项目数据
        self.load_auto_saved_project()

        return widget

    def get_current_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def closeEvent(self, event):
        """窗口关闭时自动保存"""
        self.save_current_chapter_before_switch()
        self.auto_save_project_data()  # 保存项目数据
        super().closeEvent(event)

    def on_project_data_changed(self):
        """项目数据变化时触发自动保存"""
        # 重启项目数据自动保存定时器
        self.project_save_timer.stop()
        self.project_save_timer.start(self.project_save_delay)

    def auto_save_project_data(self):
        """自动保存项目数据"""
        try:
            # 创建项目数据目录
            import os
            import json
            project_dir = "projects"
            if not os.path.exists(project_dir):
                os.makedirs(project_dir)

            # 收集所有项目数据
            project_data = {
                "title": self.novel_title.text(),
                "author": self.author_name.text(),
                "theme": self.theme_input.text(),
                "genre": self.genre_combo.currentText(),
                "chapter_count": self.chapter_count.value(),
                "outline": self.outline_output.toPlainText(),
                "chapters": self.chapters_output.toPlainText(),
                "character": self.character_output.toPlainText(),
                "world": self.world_output.toPlainText(),

                # 主角设定数据
                "protagonist": {
                    "name": self.protagonist_name.text(),
                    "gender": self.protagonist_gender.currentText(),
                    "age": self.protagonist_age.text(),
                    "occupation": self.protagonist_occupation.text()
                },

                # 章节内容数据
                "chapter_contents": getattr(self, 'chapter_contents', {}),

                # 保存时间戳
                "last_saved": self.get_current_timestamp(),
                "auto_saved": True
            }

            # 保存到自动保存文件
            auto_save_file = os.path.join(project_dir, "auto_save.json")
            with open(auto_save_file, 'w', encoding='utf-8') as f:
                json.dump(project_data, f, ensure_ascii=False, indent=2)

            # 显示自动保存状态（短暂显示）
            if hasattr(self, 'statusBar'):
                self.statusBar().showMessage("项目数据已自动保存", 1000)

        except Exception as e:
            # 自动保存失败不影响用户操作
            import logging
            logging.error(f"自动保存项目数据失败: {e}")

    def load_auto_saved_project(self):
        """加载自动保存的项目数据"""
        try:
            import os
            auto_save_file = os.path.join("projects", "auto_save.json")

            if os.path.exists(auto_save_file):
                with open(auto_save_file, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # 恢复基础信息
                self.novel_title.setText(project_data.get("title", ""))
                self.author_name.setText(project_data.get("author", ""))
                self.theme_input.setText(project_data.get("theme", ""))

                # 恢复下拉框选择
                genre = project_data.get("genre", "")
                if genre:
                    index = self.genre_combo.findText(genre)
                    if index >= 0:
                        self.genre_combo.setCurrentIndex(index)

                # 恢复章节数量
                self.chapter_count.setValue(project_data.get("chapter_count", 10))

                # 恢复内容区域
                self.outline_output.setPlainText(project_data.get("outline", ""))
                self.chapters_output.setPlainText(project_data.get("chapters", ""))
                self.character_output.setPlainText(project_data.get("character", ""))
                self.world_output.setPlainText(project_data.get("world", ""))

                # 恢复主角设定
                protagonist = project_data.get("protagonist", {})
                self.protagonist_name.setText(protagonist.get("name", ""))
                self.protagonist_age.setText(protagonist.get("age", ""))
                self.protagonist_occupation.setText(protagonist.get("occupation", ""))

                gender = protagonist.get("gender", "男")
                gender_index = self.protagonist_gender.findText(gender)
                if gender_index >= 0:
                    self.protagonist_gender.setCurrentIndex(gender_index)

                # 恢复章节内容
                chapter_contents = project_data.get("chapter_contents", {})
                if chapter_contents:
                    self.chapter_contents = chapter_contents

                # 重新解析章节列表
                chapters_text = project_data.get("chapters", "")
                if chapters_text:
                    self.parse_chapters(chapters_text)

                # 显示恢复状态
                last_saved = project_data.get("last_saved", "")
                if last_saved:
                    self.statusBar().showMessage(f"已恢复上次保存的项目数据 ({last_saved})", 3000)

        except Exception as e:
            # 加载失败不影响程序启动
            pass

    def update_word_count(self):
        """更新字数统计"""
        text = self.chapter_content.toPlainText()
        word_count = self.text_processor.get_word_count(text)
        self.word_count_label.setText(f"字数: {word_count}")

    def on_content_changed(self):
        """内容变化时触发自动保存"""
        # 只有在选择了章节时才自动保存
        if self.current_chapter_label.text() != "未选择":
            # 重启自动保存定时器
            self.auto_save_timer.stop()
            self.auto_save_timer.start(self.auto_save_delay)

    def auto_save_current_chapter(self):
        """自动保存当前章节"""
        current_chapter = self.current_chapter_label.text()
        if current_chapter == "未选择":
            return

        content = self.chapter_content.toPlainText()
        if not content.strip():
            return

        # 保存到内存缓存
        if not hasattr(self, 'chapter_contents'):
            self.chapter_contents = {}

        self.chapter_contents[current_chapter] = content

        # 显示自动保存状态
        self.statusBar().showMessage(f"自动保存: {current_chapter}", 2000)

        # 可选：同时保存到文件
        try:
            import os
            chapters_dir = "chapters"
            if not os.path.exists(chapters_dir):
                os.makedirs(chapters_dir)

            # 生成安全的文件名
            safe_filename = "".join(c for c in current_chapter if c.isalnum() or c in (' ', '-', '_')).rstrip()
            file_path = os.path.join(chapters_dir, f"{safe_filename}.txt")

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

        except Exception as e:
            # 文件保存失败不影响内存保存
            pass

    def generate_outline(self):
        """生成故事大纲"""
        theme = self.theme_input.text().strip()
        if not theme:
            QMessageBox.warning(self, "输入错误", "请输入故事主题")
            return

        genre = self.genre_combo.currentText()

        self.start_generation("outline",
                            theme=theme,
                            genre=genre,
                            length="中篇")

    def generate_chapters(self):
        """生成章节规划"""
        outline = self.outline_output.toPlainText().strip()
        if not outline:
            QMessageBox.warning(self, "输入错误", "请先生成故事大纲")
            return

        total_chapters = self.chapter_count.value()

        self.start_generation("chapters",
                            outline=outline,
                            total_chapters=total_chapters)

    def generate_character(self):
        """生成角色设定"""
        # 获取自定义主角信息
        character_name = self.protagonist_name.text().strip() or "主角"
        character_gender = self.protagonist_gender.currentText()
        character_age = self.protagonist_age.text().strip()
        character_occupation = self.protagonist_occupation.text().strip()

        story_context = self.outline_output.toPlainText()[:500]

        if not story_context:
            QMessageBox.warning(self, "输入错误", "请先生成故事大纲")
            return

        # 构建角色背景信息
        character_background = {
            "name": character_name,
            "gender": character_gender,
            "age": character_age,
            "occupation": character_occupation
        }

        self.start_generation("character",
                            character_name=character_name,
                            character_background=character_background,
                            role="主角",
                            story_context=story_context)

    def generate_world(self):
        """生成世界观设定"""
        genre = self.genre_combo.currentText()
        setting = self.outline_output.toPlainText()[:300]

        if not setting:
            QMessageBox.warning(self, "输入错误", "请先生成故事大纲")
            return

        self.start_generation("world",
                            genre=genre,
                            setting=setting)

    def continue_writing(self):
        """续写章节"""
        current_chapter = self.current_chapter_label.text()
        if current_chapter == "未选择":
            QMessageBox.warning(self, "选择错误", "请先选择要续写的章节")
            return

        previous_content = self.chapter_content.toPlainText()
        chapter_outline = current_chapter

        # 如果没有现有内容，提示用户
        if not previous_content.strip():
            reply = QMessageBox.question(
                self, "确认续写",
                "当前章节没有内容，是否要开始新的章节写作？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.Yes
            )
            if reply == QMessageBox.No:
                return

        # 获取主角信息用于续写
        protagonist_name = self.protagonist_name.text().strip() or "主角"
        protagonist_info = {
            "name": protagonist_name,
            "gender": self.protagonist_gender.currentText(),
            "age": self.protagonist_age.text().strip(),
            "occupation": self.protagonist_occupation.text().strip()
        }

        self.start_generation("continue",
                            previous_content=previous_content,
                            chapter_outline=chapter_outline,
                            protagonist_info=protagonist_info,
                            target_length=2000)

    def optimize_chapter(self):
        """优化章节"""
        chapter_content = self.chapter_content.toPlainText().strip()
        if not chapter_content:
            QMessageBox.warning(self, "输入错误", "请先输入章节内容")
            return

        optimization_focus = "提升文字质量和可读性"

        self.start_generation("optimize",
                            chapter_content=chapter_content,
                            optimization_focus=optimization_focus)

    def start_generation(self, task_type, **kwargs):
        """开始生成任务"""
        # 禁用相关按钮
        self.set_buttons_enabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # 更新状态
        task_names = {
            "outline": "故事大纲",
            "chapters": "章节规划",
            "continue": "章节续写",
            "character": "角色设定",
            "world": "世界观设定",
            "optimize": "章节优化"
        }
        task_name = task_names.get(task_type, "内容")
        self.statusBar().showMessage(f"正在生成{task_name}...")

        # 创建工作线程
        self.worker = NovelGenerationWorker(self.novel_creator, task_type, **kwargs)
        self.worker.finished.connect(self.on_generation_finished)
        self.worker.error.connect(self.on_generation_error)
        self.worker.start()

    def on_generation_finished(self, result, task_type):
        """生成完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.set_buttons_enabled(True)

        # 显示结果
        if task_type == "outline":
            self.outline_output.setPlainText(result)
            self.tab_widget.setCurrentIndex(0)
            self.statusBar().showMessage("故事大纲生成完成")
        elif task_type == "chapters":
            self.chapters_output.setPlainText(result)
            self.tab_widget.setCurrentIndex(1)
            self.parse_chapters(result)
            self.statusBar().showMessage("章节规划生成完成")
        elif task_type == "continue":
            self.chapter_content.setPlainText(result)
            self.tab_widget.setCurrentIndex(2)
            self.statusBar().showMessage("章节续写完成")

            # 续写完成后自动保存
            self.auto_save_current_chapter()
        elif task_type == "character":
            self.character_output.setPlainText(result)
            self.tab_widget.setCurrentIndex(3)
            self.statusBar().showMessage("角色设定生成完成")
        elif task_type == "world":
            self.world_output.setPlainText(result)
            self.tab_widget.setCurrentIndex(4)
            self.statusBar().showMessage("世界观设定生成完成")
        elif task_type == "optimize":
            self.chapter_content.setPlainText(result)
            self.statusBar().showMessage("章节优化完成")

            # 优化完成后自动保存
            self.auto_save_current_chapter()

    def on_generation_error(self, error_msg):
        """生成错误"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.set_buttons_enabled(True)

        # 显示错误
        QMessageBox.critical(self, "生成失败", f"生成过程中出现错误:\n{error_msg}")
        self.statusBar().showMessage("生成失败")

    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.generate_outline_btn.setEnabled(enabled)
        self.generate_chapters_btn.setEnabled(enabled)
        self.generate_character_btn.setEnabled(enabled)
        self.generate_world_btn.setEnabled(enabled)
        self.continue_writing_btn.setEnabled(enabled)
        self.optimize_chapter_btn.setEnabled(enabled)

    def parse_chapters(self, chapters_text):
        """解析章节规划并添加到列表"""
        self.chapter_list.clear()

        # 改进的章节解析逻辑
        lines = chapters_text.split('\n')
        chapter_count = 0

        for line in lines:
            line = line.strip()
            if not line:
                continue

            # 多种章节标题格式匹配
            chapter_patterns = [
                r'【第.*?章.*?】',  # 【第X章：标题】
                r'第.*?章.*?[:：]',  # 第X章：标题
                r'Chapter\s*\d+',   # Chapter 1
                r'\d+\.\s*',        # 1. 标题
                r'##\s*第.*?章',    # ## 第X章
            ]

            is_chapter = False
            for pattern in chapter_patterns:
                import re
                if re.search(pattern, line, re.IGNORECASE):
                    is_chapter = True
                    break

            # 如果匹配到章节标题
            if is_chapter:
                chapter_count += 1
                # 清理标题格式
                chapter_title = line
                if chapter_title.startswith('##'):
                    chapter_title = chapter_title[2:].strip()
                if chapter_title.startswith(('1.', '2.', '3.', '4.', '5.', '6.', '7.', '8.', '9.', '0.')):
                    chapter_title = re.sub(r'^\d+\.\s*', '', chapter_title)

                # 如果标题太短，添加默认格式
                if len(chapter_title) < 3:
                    chapter_title = f"第{chapter_count}章"

                item = QListWidgetItem(chapter_title)
                item.setData(Qt.UserRole, chapter_count)  # 存储章节编号
                self.chapter_list.addItem(item)

            # 如果没有找到明确的章节标题，但内容看起来像章节
            elif (len(line) > 5 and len(line) < 50 and
                  not line.startswith(('主要内容', '角色发展', '情节推进', '字数预估', '关键场景', '悬念设置')) and
                  chapter_count == 0):  # 只在还没找到章节时尝试
                chapter_count += 1
                chapter_title = f"第{chapter_count}章：{line}"
                item = QListWidgetItem(chapter_title)
                item.setData(Qt.UserRole, chapter_count)
                self.chapter_list.addItem(item)

        # 如果没有解析到任何章节，创建默认章节
        if chapter_count == 0:
            for i in range(1, 6):  # 创建5个默认章节
                chapter_title = f"第{i}章：待编写"
                item = QListWidgetItem(chapter_title)
                item.setData(Qt.UserRole, i)
                self.chapter_list.addItem(item)

            self.statusBar().showMessage("未能解析章节标题，已创建默认章节列表")
        else:
            self.statusBar().showMessage(f"成功解析 {chapter_count} 个章节")

        # 更新章节选择下拉框
        self.update_chapter_selector()

    def update_chapter_selector(self):
        """更新章节选择下拉框"""
        if not hasattr(self, 'chapter_selector'):
            return

        # 保存当前选择
        current_text = self.chapter_selector.currentText()

        # 清空并重新填充
        self.chapter_selector.clear()
        self.chapter_selector.addItem("请选择章节...")

        # 添加所有章节
        for i in range(self.chapter_list.count()):
            item = self.chapter_list.item(i)
            self.chapter_selector.addItem(item.text())

        # 恢复选择
        if current_text:
            index = self.chapter_selector.findText(current_text)
            if index >= 0:
                self.chapter_selector.setCurrentIndex(index)

    def on_chapter_selected(self, chapter_title):
        """章节选择改变"""
        # 先保存当前章节内容（如果有的话）
        self.save_current_chapter_before_switch()

        if chapter_title == "请选择章节..." or not chapter_title:
            self.current_chapter_label.setText("未选择")
            self.chapter_content.clear()
            return

        # 更新当前章节标签
        self.current_chapter_label.setText(chapter_title)

        # 查找对应的章节项
        for i in range(self.chapter_list.count()):
            item = self.chapter_list.item(i)
            if item.text() == chapter_title:
                self.chapter_list.setCurrentItem(item)
                break

        # 加载章节内容（如果有保存的话）
        self.load_chapter_content(chapter_title)

    def save_current_chapter_before_switch(self):
        """切换章节前保存当前章节内容"""
        current_chapter = self.current_chapter_label.text()
        if current_chapter == "未选择":
            return

        content = self.chapter_content.toPlainText()
        if not content.strip():
            return

        # 保存到内存缓存
        if not hasattr(self, 'chapter_contents'):
            self.chapter_contents = {}

        # 只有内容发生变化时才保存
        old_content = self.chapter_contents.get(current_chapter, "")
        if content != old_content:
            self.chapter_contents[current_chapter] = content

            # 同时保存到文件
            try:
                import os
                chapters_dir = "chapters"
                if not os.path.exists(chapters_dir):
                    os.makedirs(chapters_dir)

                safe_filename = "".join(c for c in current_chapter if c.isalnum() or c in (' ', '-', '_')).rstrip()
                file_path = os.path.join(chapters_dir, f"{safe_filename}.txt")

                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)

                self.statusBar().showMessage(f"已保存: {current_chapter}", 1500)

            except Exception as e:
                # 文件保存失败不影响内存保存
                pass

    def edit_chapter(self, item):
        """编辑章节（双击章节列表时调用）"""
        chapter_title = item.text()

        # 更新下拉框选择
        index = self.chapter_selector.findText(chapter_title)
        if index >= 0:
            self.chapter_selector.setCurrentIndex(index)

        # 更新当前章节标签
        self.current_chapter_label.setText(chapter_title)

        # 切换到章节编写标签页
        self.tab_widget.setCurrentIndex(2)

        # 加载章节内容
        self.load_chapter_content(chapter_title)

    def load_chapter_content(self, chapter_title):
        """加载章节内容"""
        # 这里可以从文件或数据库加载章节内容
        # 暂时使用简单的内存存储
        if not hasattr(self, 'chapter_contents'):
            self.chapter_contents = {}

        content = self.chapter_contents.get(chapter_title, "")
        self.chapter_content.setPlainText(content)

    def save_current_chapter(self):
        """保存当前章节内容"""
        current_chapter = self.current_chapter_label.text()
        if current_chapter == "未选择":
            QMessageBox.warning(self, "警告", "请先选择要保存的章节")
            return

        content = self.chapter_content.toPlainText()
        if not content.strip():
            QMessageBox.warning(self, "警告", "章节内容为空")
            return

        # 保存到内存（可以扩展为文件保存）
        if not hasattr(self, 'chapter_contents'):
            self.chapter_contents = {}

        self.chapter_contents[current_chapter] = content

        # 可选：保存到文件
        try:
            import os
            chapters_dir = "chapters"
            if not os.path.exists(chapters_dir):
                os.makedirs(chapters_dir)

            # 生成安全的文件名
            safe_filename = "".join(c for c in current_chapter if c.isalnum() or c in (' ', '-', '_')).rstrip()
            file_path = os.path.join(chapters_dir, f"{safe_filename}.txt")

            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)

            QMessageBox.information(self, "成功", f"章节已保存到: {file_path}")
            self.statusBar().showMessage(f"章节已保存: {current_chapter}")

        except Exception as e:
            QMessageBox.warning(self, "保存失败", f"保存到文件失败: {e}\n内容已保存到内存中")
            self.statusBar().showMessage(f"章节已保存到内存: {current_chapter}")

    def add_chapter(self):
        """添加章节"""
        dialog = ChapterDialog(self)
        if dialog.exec() == QDialog.Accepted:
            title, content = dialog.get_data()
            if title:
                item = QListWidgetItem(title)
                self.chapter_list.addItem(item)

                # 更新章节选择下拉框
                self.update_chapter_selector()

                # 如果有内容，保存它
                if content and content.strip():
                    if not hasattr(self, 'chapter_contents'):
                        self.chapter_contents = {}
                    self.chapter_contents[title] = content

    def delete_chapter(self):
        """删除章节"""
        current_item = self.chapter_list.currentItem()
        if current_item:
            chapter_title = current_item.text()
            reply = QMessageBox.question(
                self, "确认删除",
                f"确定要删除章节 '{chapter_title}' 吗？",
                QMessageBox.Yes | QMessageBox.No,
                QMessageBox.No
            )
            if reply == QMessageBox.Yes:
                # 从列表中删除
                row = self.chapter_list.row(current_item)
                self.chapter_list.takeItem(row)

                # 从内存中删除章节内容
                if hasattr(self, 'chapter_contents') and chapter_title in self.chapter_contents:
                    del self.chapter_contents[chapter_title]

                # 更新章节选择下拉框
                self.update_chapter_selector()

                # 如果删除的是当前选择的章节，清空选择
                if self.current_chapter_label.text() == chapter_title:
                    self.current_chapter_label.setText("未选择")
                    self.chapter_content.clear()
                    if hasattr(self, 'chapter_selector'):
                        self.chapter_selector.setCurrentIndex(0)  # 选择"请选择章节..."

                self.statusBar().showMessage(f"已删除章节: {chapter_title}")

    def save_content(self, content_type):
        """保存内容"""
        content_map = {
            "outline": (self.outline_output.toPlainText(), "故事大纲.txt"),
            "chapters": (self.chapters_output.toPlainText(), "章节规划.txt")
        }

        if content_type not in content_map:
            return

        content, default_name = content_map[content_type]

        if not content.strip():
            QMessageBox.warning(self, "警告", "没有内容可保存")
            return

        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存文件", default_name,
            "文本文件 (*.txt);;所有文件 (*.*)"
        )

        if file_path:
            success, error_msg = self.text_processor.save_to_file(content, file_path)
            if success:
                QMessageBox.information(self, "成功", "文件保存成功")
                self.statusBar().showMessage(f"已保存: {Path(file_path).name}")
            else:
                QMessageBox.critical(self, "错误", error_msg)

    def new_project(self):
        """新建项目"""
        reply = QMessageBox.question(
            self, "新建项目",
            "确定要新建项目吗？当前内容将被清空。",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.clear_all_content()

    def clear_all_content(self):
        """清空所有内容"""
        self.novel_title.clear()
        self.author_name.clear()
        self.theme_input.clear()
        self.outline_output.clear()
        self.chapters_output.clear()
        self.chapter_content.clear()
        self.character_output.clear()
        self.world_output.clear()
        self.chapter_list.clear()
        self.current_chapter_label.setText("未选择")

        # 清空主角设定
        self.protagonist_name.clear()
        self.protagonist_age.clear()
        self.protagonist_occupation.clear()
        self.protagonist_gender.setCurrentIndex(0)  # 重置为"男"

        # 重置章节数量
        self.chapter_count.setValue(10)

        # 重置类型选择
        self.genre_combo.setCurrentIndex(0)

        # 清空章节内容缓存
        if hasattr(self, 'chapter_contents'):
            self.chapter_contents.clear()

        # 清空章节选择下拉框
        if hasattr(self, 'chapter_selector'):
            self.chapter_selector.clear()
            self.chapter_selector.addItem("请选择章节...")

        # 清空自动保存的项目数据
        try:
            import os
            auto_save_file = os.path.join("projects", "auto_save.json")
            if os.path.exists(auto_save_file):
                os.remove(auto_save_file)
        except:
            pass

    def open_project(self):
        """打开项目"""
        # 简化实现，可以扩展为完整的项目文件格式
        file_path, _ = QFileDialog.getOpenFileName(
            self, "打开项目", "",
            "JSON文件 (*.json);;所有文件 (*.*)"
        )
        if file_path:
            try:
                with open(file_path, 'r', encoding='utf-8') as f:
                    project_data = json.load(f)

                # 加载项目数据
                self.novel_title.setText(project_data.get("title", ""))
                self.author_name.setText(project_data.get("author", ""))
                self.theme_input.setText(project_data.get("theme", ""))
                self.outline_output.setPlainText(project_data.get("outline", ""))
                self.chapters_output.setPlainText(project_data.get("chapters", ""))
                self.character_output.setPlainText(project_data.get("character", ""))
                self.world_output.setPlainText(project_data.get("world", ""))

                # 加载主角设定
                protagonist = project_data.get("protagonist", {})
                if protagonist:
                    self.protagonist_name.setText(protagonist.get("name", ""))
                    self.protagonist_age.setText(protagonist.get("age", ""))
                    self.protagonist_occupation.setText(protagonist.get("occupation", ""))

                    gender = protagonist.get("gender", "男")
                    gender_index = self.protagonist_gender.findText(gender)
                    if gender_index >= 0:
                        self.protagonist_gender.setCurrentIndex(gender_index)

                # 加载其他设置
                if "genre" in project_data:
                    genre_index = self.genre_combo.findText(project_data["genre"])
                    if genre_index >= 0:
                        self.genre_combo.setCurrentIndex(genre_index)

                if "chapter_count" in project_data:
                    self.chapter_count.setValue(project_data["chapter_count"])

                self.statusBar().showMessage(f"已打开项目: {Path(file_path).name}")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"打开项目失败: {e}")

    def save_project(self):
        """保存项目"""
        file_path, _ = QFileDialog.getSaveFileName(
            self, "保存项目", f"{self.novel_title.text() or '未命名项目'}.json",
            "JSON文件 (*.json);;所有文件 (*.*)"
        )
        if file_path:
            try:
                project_data = {
                    "title": self.novel_title.text(),
                    "author": self.author_name.text(),
                    "theme": self.theme_input.text(),
                    "genre": self.genre_combo.currentText(),
                    "chapter_count": self.chapter_count.value(),
                    "outline": self.outline_output.toPlainText(),
                    "chapters": self.chapters_output.toPlainText(),
                    "character": self.character_output.toPlainText(),
                    "world": self.world_output.toPlainText(),

                    # 主角设定数据
                    "protagonist": {
                        "name": self.protagonist_name.text(),
                        "gender": self.protagonist_gender.currentText(),
                        "age": self.protagonist_age.text(),
                        "occupation": self.protagonist_occupation.text()
                    },

                    # 章节内容数据
                    "chapter_contents": getattr(self, 'chapter_contents', {}),

                    # 保存时间戳
                    "last_saved": self.get_current_timestamp(),
                    "version": "2.0"
                }

                with open(file_path, 'w', encoding='utf-8') as f:
                    json.dump(project_data, f, ensure_ascii=False, indent=2)

                self.statusBar().showMessage(f"已保存项目: {Path(file_path).name}")
                QMessageBox.information(self, "成功", "项目保存成功")

            except Exception as e:
                QMessageBox.critical(self, "错误", f"保存项目失败: {e}")

    def export_novel(self):
        """导出小说"""
        # 收集所有章节内容
        novel_content = f"《{self.novel_title.text()}》\n\n"
        novel_content += f"作者：{self.author_name.text()}\n\n"

        # 添加大纲
        outline = self.outline_output.toPlainText()
        if outline:
            novel_content += "【故事大纲】\n" + outline + "\n\n"

        # 添加章节内容（这里简化处理）
        novel_content += "【正文】\n"
        novel_content += self.chapter_content.toPlainText()

        file_path, _ = QFileDialog.getSaveFileName(
            self, "导出小说", f"{self.novel_title.text() or '未命名小说'}.txt",
            "文本文件 (*.txt);;所有文件 (*.*)"
        )

        if file_path:
            success, error_msg = self.text_processor.save_to_file(novel_content, file_path)
            if success:
                QMessageBox.information(self, "成功", "小说导出成功")
                self.statusBar().showMessage(f"已导出: {Path(file_path).name}")
            else:
                QMessageBox.critical(self, "错误", error_msg)
