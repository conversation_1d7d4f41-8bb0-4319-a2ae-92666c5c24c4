# 小说创作工作室主角设定和数据自动保存功能说明

## 🎯 功能概述

### 新增功能1: 自定义主角设定
- 在角色设定标签页添加主角信息输入区域
- 支持设置主角姓名、性别、年龄、职业
- AI生成角色设定和章节续写时自动使用主角信息
- 主角信息与项目数据一起自动保存

### 新增功能2: 项目数据自动保存
- 所有界面输入的数据实时自动保存
- 程序重启后自动恢复上次的工作状态
- 支持小说标题、作者、主题、大纲等全部数据
- 章节内容和主角设定同步保存

## 🎭 主角设定功能详解

### 1. 主角信息输入界面

#### 界面布局
```
角色设定标签页:
├── 主角设定区域 (GroupBox)
│   ├── 主角姓名: [输入框]     主角性别: [下拉框]
│   ├── 主角年龄: [输入框]     主角职业: [输入框]
│   └── [生成角色设定] 按钮
└── 角色设定输出区域 (TextEdit)
```

#### 输入字段说明
- **主角姓名**: 自定义主角的名字，如"李明"、"张小雨"等
- **主角性别**: 下拉选择"男"、"女"、"其他"
- **主角年龄**: 主角年龄描述，如"25岁"、"十八岁"等
- **主角职业**: 主角的职业背景，如"程序员"、"学生"、"医生"等

### 2. AI集成功能

#### 角色生成集成
```python
def generate_character(self):
    # 获取自定义主角信息
    character_name = self.protagonist_name.text().strip() or "主角"
    character_background = {
        "name": character_name,
        "gender": self.protagonist_gender.currentText(),
        "age": self.protagonist_age.text().strip(),
        "occupation": self.protagonist_occupation.text().strip()
    }
    
    # 传递给AI生成器
    self.start_generation("character", 
                         character_background=character_background)
```

#### 章节续写集成
```python
def continue_writing(self):
    # 获取主角信息用于续写
    protagonist_info = {
        "name": self.protagonist_name.text().strip() or "主角",
        "gender": self.protagonist_gender.currentText(),
        "age": self.protagonist_age.text().strip(),
        "occupation": self.protagonist_occupation.text().strip()
    }
    
    # 传递给续写功能
    self.start_generation("continue", 
                         protagonist_info=protagonist_info)
```

### 3. 使用流程

#### 设置主角信息
1. 打开小说创作工作室
2. 切换到"角色设定"标签页
3. 在"主角设定"区域填写主角信息：
   - 输入主角姓名
   - 选择主角性别
   - 填写主角年龄
   - 输入主角职业
4. 信息会自动保存（3秒延迟）

#### 生成角色设定
1. 确保已生成故事大纲
2. 填写主角基本信息
3. 点击"生成角色设定"按钮
4. AI会基于主角信息生成详细的角色设定

#### 章节续写使用
1. 选择要续写的章节
2. 确保主角信息已填写
3. 点击"续写章节"按钮
4. AI会在续写时使用主角的姓名和背景

## 💾 项目数据自动保存功能详解

### 1. 自动保存机制

#### 保存触发条件
- **内容变化**: 任何输入框或文本区域内容变化
- **选择变化**: 下拉框选择改变
- **数值变化**: 章节数量等数值改变
- **定时保存**: 内容变化3秒后自动保存
- **程序关闭**: 窗口关闭时立即保存

#### 保存的数据范围
```json
{
  "title": "小说标题",
  "author": "作者名字",
  "theme": "故事主题",
  "genre": "小说类型",
  "chapter_count": 10,
  "outline": "故事大纲内容",
  "chapters": "章节规划内容",
  "character": "角色设定内容",
  "world": "世界观设定内容",
  
  "protagonist": {
    "name": "主角姓名",
    "gender": "主角性别",
    "age": "主角年龄",
    "occupation": "主角职业"
  },
  
  "chapter_contents": {
    "第一章": "章节内容...",
    "第二章": "章节内容..."
  },
  
  "last_saved": "2024-01-01 12:00:00",
  "auto_saved": true
}
```

### 2. 数据恢复机制

#### 自动恢复流程
```
程序启动 → 检查自动保存文件 → 恢复所有数据 → 显示恢复状态
```

#### 恢复的内容
- ✅ 小说基本信息（标题、作者、主题等）
- ✅ 故事创作内容（大纲、章节规划等）
- ✅ 角色和世界观设定
- ✅ 主角详细信息
- ✅ 所有章节内容
- ✅ 界面选择状态（类型、章节数等）

### 3. 文件管理

#### 保存位置
- **项目数据**: `projects/auto_save.json`
- **章节内容**: `chapters/章节名.txt`

#### 文件格式
- **编码**: UTF-8（支持中文）
- **格式**: JSON（结构化数据）
- **备份**: 自动覆盖保存

## 🚀 技术实现

### 1. 防抖自动保存

#### 项目数据保存
```python
def on_project_data_changed(self):
    """项目数据变化时触发自动保存"""
    # 重启3秒定时器（防抖）
    self.project_save_timer.stop()
    self.project_save_timer.start(3000)
```

#### 章节内容保存
```python
def on_content_changed(self):
    """章节内容变化时触发自动保存"""
    # 重启2秒定时器（防抖）
    self.auto_save_timer.stop()
    self.auto_save_timer.start(2000)
```

### 2. 数据同步机制

#### 信号连接
```python
# 连接所有数据变化信号
self.novel_title.textChanged.connect(self.on_project_data_changed)
self.author_name.textChanged.connect(self.on_project_data_changed)
self.protagonist_name.textChanged.connect(self.on_project_data_changed)
# ... 其他控件
```

#### 实时保存
- **输入延迟**: 用户停止输入后才保存
- **状态反馈**: 状态栏显示保存状态
- **错误处理**: 保存失败不影响用户操作

### 3. 数据完整性

#### 保存验证
- 检查数据有效性
- 创建必要的目录
- 处理文件权限问题

#### 恢复验证
- 检查文件存在性
- 验证JSON格式
- 处理数据缺失情况

## 📋 使用指南

### 1. 主角设定使用流程

#### 步骤1: 设置主角基本信息
```
1. 打开小说创作工作室
2. 切换到"角色设定"标签页
3. 在主角设定区域填写：
   - 主角姓名: 如"李明"
   - 主角性别: 选择"男/女/其他"
   - 主角年龄: 如"25岁"
   - 主角职业: 如"程序员"
4. 信息自动保存（状态栏显示"项目数据已自动保存"）
```

#### 步骤2: 生成角色设定
```
1. 确保已在"故事规划"中生成故事大纲
2. 在角色设定页面点击"生成角色设定"
3. AI会基于主角信息生成详细角色设定
4. 生成的内容会自动保存
```

#### 步骤3: 在续写中使用
```
1. 切换到"章节编写"标签页
2. 选择要续写的章节
3. 点击"续写章节"按钮
4. AI会在续写时使用主角姓名和背景信息
```

### 2. 数据自动保存使用

#### 正常工作流程
```
1. 启动程序 → 自动恢复上次数据
2. 编辑任何内容 → 3秒后自动保存
3. 切换标签页 → 当前内容自动保存
4. 关闭程序 → 所有数据自动保存
5. 重新启动 → 自动恢复到上次状态
```

#### 状态监控
- **保存提示**: 状态栏显示"项目数据已自动保存"
- **恢复提示**: 启动时显示"已恢复上次保存的项目数据"
- **时间戳**: 显示最后保存时间

### 3. 项目管理

#### 手动保存项目
```
1. 点击"文件" → "保存项目"
2. 选择保存位置和文件名
3. 项目数据保存为JSON文件
4. 包含所有内容和主角设定
```

#### 打开已保存项目
```
1. 点击"文件" → "打开项目"
2. 选择项目JSON文件
3. 所有数据自动加载
4. 主角设定和章节内容恢复
```

## 🎉 功能优势

### 1. 用户体验提升

#### 修复前的问题
- ❌ 无法自定义主角信息
- ❌ AI生成内容使用通用称呼
- ❌ 数据容易丢失
- ❌ 需要重复输入信息

#### 修复后的优势
- ✅ **个性化主角** - 自定义主角姓名和背景
- ✅ **AI智能集成** - 生成内容使用真实主角信息
- ✅ **数据永不丢失** - 实时自动保存所有数据
- ✅ **无缝工作流** - 程序重启后自动恢复状态

### 2. 创作效率提升

#### 主角设定效率
- **一次设置** - 主角信息设置一次，全程使用
- **AI集成** - 角色生成和续写自动使用主角信息
- **个性化内容** - 生成的内容更贴合主角设定

#### 数据管理效率
- **零操作负担** - 完全自动化的数据保存
- **即时恢复** - 程序重启后立即恢复工作状态
- **多重保护** - 项目数据和章节内容双重保存

### 3. 创作质量提升

#### 角色一致性
- 主角姓名在全文中保持一致
- 角色背景信息贯穿整个故事
- AI生成内容符合角色设定

#### 内容连贯性
- 章节续写考虑主角特征
- 角色设定与故事情节匹配
- 整体创作风格统一

## 🎊 立即体验

现在您可以：

### 1. 设置个性化主角
```
1. 启动程序: python main.py
2. 打开小说创作工作室: 工具 → 小说创作工作室
3. 切换到"角色设定"标签页
4. 填写主角信息:
   - 姓名: 输入您的主角名字
   - 性别: 选择合适的性别
   - 年龄: 填写主角年龄
   - 职业: 输入主角职业
5. 观察状态栏显示"项目数据已自动保存"
```

### 2. 体验AI集成功能
```
1. 生成故事大纲（在故事规划标签页）
2. 返回角色设定标签页
3. 点击"生成角色设定"按钮
4. 查看AI生成的角色设定是否使用了您的主角信息
5. 在章节编写中使用"续写章节"功能
6. 观察续写内容是否使用了主角姓名
```

### 3. 验证自动保存功能
```
1. 输入一些项目信息（标题、作者等）
2. 等待3秒，观察状态栏提示
3. 关闭程序
4. 重新启动程序
5. 打开小说创作工作室
6. 确认所有数据都已恢复
```

**两个功能都已完全实现！现在您可以自定义主角设定，所有数据都会自动保存！** 🎊

---

**功能完成**: 小说创作工作室现在支持自定义主角设定和完整的数据自动保存功能！
