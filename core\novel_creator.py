"""
小说创作模块
支持故事大纲生成、章节规划、续写等功能
"""

import json
import logging
from typing import Dict, List, Optional
from core.ai_generator import AIGenerator

class NovelCreator:
    """小说创作器"""
    
    def __init__(self, ai_generator: AIGenerator):
        """初始化小说创作器"""
        self.ai_generator = ai_generator
        self.logger = logging.getLogger(__name__)
    
    def generate_story_outline(self, theme: str, genre: str, length: str = "中篇") -> str:
        """生成故事大纲"""
        prompt = f"""
请根据以下要求创作一个{genre}小说的详细故事大纲。

主题：{theme}
类型：{genre}
长度：{length}

请按以下格式生成故事大纲：

【故事概述】
简要描述整个故事的核心情节和主要冲突

【主要角色】
1. 主角：姓名、性格、背景、目标
2. 配角：重要配角的基本信息
3. 反派：对手角色的设定

【故事结构】
第一幕：开端（约占25%）
- 背景设定
- 角色介绍
- 引发事件

第二幕：发展（约占50%）
- 主要冲突展开
- 角色成长
- 情节转折

第三幕：高潮与结局（约占25%）
- 最终对决
- 冲突解决
- 故事收尾

【核心主题】
故事想要表达的深层含义和价值观

【创作要点】
- 关键情节点
- 需要注意的细节
- 可能的续集方向

请确保大纲逻辑清晰，情节引人入胜，角色立体丰满。
"""
        
        return self.ai_generator._call_ai_api(prompt, "story_outline")
    
    def generate_chapter_plan(self, outline: str, total_chapters: int = 20) -> str:
        """生成章节规划"""
        prompt = f"""
请根据以下故事大纲，制定详细的章节规划。

故事大纲：
{outline}

总章节数：{total_chapters}章

请按以下格式生成章节规划：

【第X章：章节标题】
主要内容：本章的核心情节和事件
角色发展：主要角色在本章的变化和成长
情节推进：本章如何推动整体故事发展
字数预估：建议字数范围
关键场景：重要的场景描述
悬念设置：章末的悬念或转折

请确保：
1. 每章都有明确的目标和作用
2. 情节发展循序渐进，节奏合理
3. 角色成长轨迹清晰
4. 悬念和冲突分布均匀
5. 整体结构完整，逻辑连贯

生成{total_chapters}章的详细规划。
"""
        
        return self.ai_generator._call_ai_api(prompt, "chapter_plan")
    
    def continue_chapter(self, previous_content: str, chapter_outline: str, target_length: int = 2000) -> str:
        """续写章节内容"""
        prompt = f"""
请根据以下信息续写小说章节。

前文内容：
{previous_content[-1000:] if len(previous_content) > 1000 else previous_content}

本章大纲：
{chapter_outline}

目标字数：约{target_length}字

续写要求：
1. 与前文风格保持一致
2. 严格按照章节大纲发展情节
3. 注重人物对话和心理描写
4. 场景描述生动具体
5. 情节推进自然流畅
6. 适当设置悬念和转折
7. 语言优美，富有感染力

请直接开始续写，不需要额外说明：
"""
        
        return self.ai_generator._call_ai_api(prompt, "chapter_content")
    
    def generate_character_profile(self, character_name: str, role: str, story_context: str) -> str:
        """生成角色档案"""
        prompt = f"""
请为小说中的角色创建详细档案。

角色姓名：{character_name}
角色定位：{role}
故事背景：{story_context}

请按以下格式生成角色档案：

【基本信息】
姓名：
年龄：
性别：
职业：
外貌特征：

【性格特点】
核心性格：
优点：
缺点：
特殊癖好：
口头禅：

【背景故事】
出生背景：
成长经历：
重要事件：
人际关系：

【能力设定】
特殊技能：
知识背景：
战斗能力：
弱点：

【角色弧光】
初始状态：
成长目标：
转变过程：
最终状态：

【对话风格】
说话特点：
常用词汇：
语气特色：

【在故事中的作用】
主要功能：
与主角关系：
推动情节的方式：

请确保角色立体丰满，有血有肉，符合故事设定。
"""
        
        return self.ai_generator._call_ai_api(prompt, "character_profile")
    
    def generate_world_building(self, genre: str, setting: str) -> str:
        """生成世界观设定"""
        prompt = f"""
请为{genre}小说创建详细的世界观设定。

基本设定：{setting}

请按以下格式生成世界观：

【世界概况】
世界名称：
基本设定：
时代背景：
科技水平：

【地理环境】
主要地区：
气候特点：
重要地标：
资源分布：

【社会结构】
政治体制：
社会阶层：
经济体系：
文化特色：

【力量体系】（如适用）
能力分类：
等级划分：
获得方式：
限制条件：

【历史背景】
重要历史事件：
传说故事：
文明发展：
当前局势：

【规则设定】
物理法则：
社会规则：
禁忌事项：
特殊现象：

【重要组织】
主要势力：
组织结构：
相互关系：
影响力：

请确保世界观设定完整、自洽，为故事发展提供坚实基础。
"""
        
        return self.ai_generator._call_ai_api(prompt, "world_building")
    
    def optimize_chapter(self, chapter_content: str, optimization_focus: str) -> str:
        """优化章节内容"""
        prompt = f"""
请对以下章节内容进行优化。

原始内容：
{chapter_content}

优化重点：{optimization_focus}

优化要求：
1. 保持原有情节框架不变
2. 提升文字表达质量
3. 增强人物形象塑造
4. 优化对话和描写
5. 改善节奏和结构
6. 修正语法和用词错误
7. 增强可读性和吸引力

请提供优化后的完整章节内容：
"""
        
        return self.ai_generator._call_ai_api(prompt, "chapter_optimization")
    
    def generate_plot_twist(self, current_plot: str, twist_type: str = "意外转折") -> str:
        """生成情节转折"""
        prompt = f"""
请为当前情节设计一个{twist_type}。

当前情节：
{current_plot}

转折要求：
1. 出人意料但合理
2. 符合前文铺垫
3. 推动故事发展
4. 增强戏剧冲突
5. 不破坏整体逻辑

请提供：
1. 转折点描述
2. 实现方式
3. 对后续情节的影响
4. 需要的前期铺垫
"""
        
        return self.ai_generator._call_ai_api(prompt, "plot_twist")
