"""
分镜图片生成窗口
内嵌网页支持豆包生图功能
"""

import os
import json
import logging
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QComboBox, QTabWidget,
    QFileDialog, QMessageBox, QProgressBar, QSplitter,
    QGroupBox, QGridLayout, QLineEdit, QListWidget,
    QListWidgetItem, QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal, QUrl
from PySide6.QtGui import QFont, QAction, QPixmap

from core.ai_generator import AIGenerator
from core.text_processor import TextProcessor
from core.webengine_setup import create_safe_webview
from core.image_extractor import ImageExtractor

class ImageGenerationWorker(QThread):
    """图片生成工作线程"""

    finished = Signal(str)  # 图片URL
    error = Signal(str)

    def __init__(self, ai_generator, prompt, style):
        super().__init__()
        self.ai_generator = ai_generator
        self.prompt = prompt
        self.style = style

    def run(self):
        try:
            result = self.ai_generator.generate_image_with_doubao(self.prompt, self.style)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))


class ImageDownloadWorker(QThread):
    """图片下载工作线程"""
    download_finished = Signal(str)  # 本地文件路径
    download_error = Signal(str, str)  # 错误信息, 原始URL

    def __init__(self, image_url):
        super().__init__()
        self.image_url = image_url

    def run(self):
        try:
            import requests
            import tempfile
            from datetime import datetime
            from urllib.parse import urlparse

            # 设置请求头
            headers = {
                'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36'
            }

            # 下载图片
            response = requests.get(self.image_url, headers=headers, timeout=30)
            response.raise_for_status()

            # 确定文件扩展名
            content_type = response.headers.get('content-type', '')
            if 'png' in content_type:
                ext = '.png'
            elif 'jpeg' in content_type or 'jpg' in content_type:
                ext = '.jpg'
            elif 'gif' in content_type:
                ext = '.gif'
            elif 'webp' in content_type:
                ext = '.webp'
            else:
                # 从URL推断
                parsed_url = urlparse(self.image_url)
                path = parsed_url.path.lower()
                if path.endswith('.png'):
                    ext = '.png'
                elif path.endswith(('.jpg', '.jpeg')):
                    ext = '.jpg'
                elif path.endswith('.gif'):
                    ext = '.gif'
                elif path.endswith('.webp'):
                    ext = '.webp'
                else:
                    ext = '.png'  # 默认

            # 生成临时文件名
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")[:-3]
            temp_dir = tempfile.gettempdir()
            local_path = os.path.join(temp_dir, f"downloaded_image_{timestamp}{ext}")

            # 保存文件
            with open(local_path, 'wb') as f:
                f.write(response.content)

            # 验证文件
            if os.path.exists(local_path) and os.path.getsize(local_path) > 0:
                self.download_finished.emit(local_path)
            else:
                self.download_error.emit("下载的文件无效", self.image_url)

        except requests.exceptions.RequestException as e:
            self.download_error.emit(f"网络请求失败: {e}", self.image_url)
        except Exception as e:
            self.download_error.emit(f"下载失败: {e}", self.image_url)

class ImageGeneratorWindow(QMainWindow):
    """分镜图片生成主窗口"""
    
    def __init__(self):
        super().__init__()
        self.ai_generator = AIGenerator()
        self.text_processor = TextProcessor()
        self.worker = None
        self.storyboard_scenes = []
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("分镜图片生成器")
        self.setGeometry(100, 100, 1600, 1000)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制区域
        left_widget = self.create_control_area()
        splitter.addWidget(left_widget)
        
        # 右侧显示区域
        right_widget = self.create_display_area()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([500, 1100])
        
        # 状态栏
        self.statusBar().showMessage("分镜图片生成器已就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
        
        # 创建菜单栏
        self.create_menu_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        import_action = QAction("导入分镜脚本", self)
        import_action.triggered.connect(self.import_storyboard)
        file_menu.addAction(import_action)
        
        export_action = QAction("导出图片", self)
        export_action.triggered.connect(self.export_images)
        file_menu.addAction(export_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具")
        
        batch_generate_action = QAction("批量生成", self)
        batch_generate_action.triggered.connect(self.batch_generate)
        tools_menu.addAction(batch_generate_action)
    
    def create_control_area(self):
        """创建控制区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 分镜脚本输入组
        script_group = QGroupBox("分镜脚本")
        script_layout = QVBoxLayout(script_group)
        
        # 导入按钮
        import_layout = QHBoxLayout()
        self.import_script_btn = QPushButton("导入脚本")
        self.import_script_btn.clicked.connect(self.import_storyboard)
        import_layout.addWidget(self.import_script_btn)
        
        self.parse_script_btn = QPushButton("解析脚本")
        self.parse_script_btn.clicked.connect(self.parse_storyboard)
        import_layout.addWidget(self.parse_script_btn)
        import_layout.addStretch()
        script_layout.addLayout(import_layout)
        
        # 脚本内容
        self.script_input = QTextEdit()
        self.script_input.setPlaceholderText("请输入或导入分镜脚本...")
        self.script_input.setMaximumHeight(200)
        script_layout.addWidget(self.script_input)
        
        layout.addWidget(script_group)
        
        # 生成设置组
        settings_group = QGroupBox("生成设置")
        settings_layout = QGridLayout(settings_group)
        
        settings_layout.addWidget(QLabel("图片风格:"), 0, 0)
        self.style_combo = QComboBox()
        self.style_combo.addItems([
            "realistic", "anime", "cartoon", "sketch", 
            "oil_painting", "watercolor", "digital_art", "3d_render"
        ])
        settings_layout.addWidget(self.style_combo, 0, 1)
        
        settings_layout.addWidget(QLabel("图片尺寸:"), 1, 0)
        self.size_combo = QComboBox()
        self.size_combo.addItems(["1024x1024", "1024x768", "768x1024", "1280x720"])
        settings_layout.addWidget(self.size_combo, 1, 1)
        
        layout.addWidget(settings_group)
        
        # 场景列表组
        scenes_group = QGroupBox("分镜场景")
        scenes_layout = QVBoxLayout(scenes_group)
        
        self.scene_list = QListWidget()
        self.scene_list.itemClicked.connect(self.select_scene)
        scenes_layout.addWidget(self.scene_list)
        
        # 场景操作按钮
        scene_buttons = QHBoxLayout()
        self.generate_single_btn = QPushButton("生成当前")
        self.generate_single_btn.clicked.connect(self.generate_single_image)
        scene_buttons.addWidget(self.generate_single_btn)
        
        self.generate_all_btn = QPushButton("生成全部")
        self.generate_all_btn.clicked.connect(self.generate_all_images)
        scene_buttons.addWidget(self.generate_all_btn)
        scenes_layout.addLayout(scene_buttons)
        
        layout.addWidget(scenes_group)
        
        # 当前场景详情
        current_group = QGroupBox("当前场景")
        current_layout = QVBoxLayout(current_group)
        
        self.current_scene_label = QLabel("未选择场景")
        current_layout.addWidget(self.current_scene_label)
        
        self.scene_prompt = QTextEdit()
        self.scene_prompt.setPlaceholderText("场景描述将显示在这里...")
        self.scene_prompt.setMaximumHeight(100)
        current_layout.addWidget(self.scene_prompt)
        
        # 提示词优化
        prompt_buttons = QHBoxLayout()
        self.optimize_prompt_btn = QPushButton("优化提示词")
        self.optimize_prompt_btn.clicked.connect(self.optimize_prompt)
        prompt_buttons.addWidget(self.optimize_prompt_btn)

        # 添加测试按钮
        self.test_generate_btn = QPushButton("测试生成")
        self.test_generate_btn.clicked.connect(self.test_generate_image)
        prompt_buttons.addWidget(self.test_generate_btn)

        prompt_buttons.addStretch()
        current_layout.addLayout(prompt_buttons)
        
        layout.addWidget(current_group)
        
        layout.addStretch()
        
        return widget
    
    def create_display_area(self):
        """创建显示区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 豆包生图网页标签页
        web_tab = QWidget()
        web_layout = QVBoxLayout(web_tab)
        
        # 网页控制按钮
        web_controls = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_web)
        web_controls.addWidget(self.refresh_btn)

        self.home_btn = QPushButton("返回首页")
        self.home_btn.clicked.connect(self.go_home)
        web_controls.addWidget(self.home_btn)

        # 添加高级图片提取按钮
        self.extract_all_btn = QPushButton("提取所有图片")
        self.extract_all_btn.clicked.connect(lambda: self.extract_images("all"))
        web_controls.addWidget(self.extract_all_btn)

        self.extract_ai_btn = QPushButton("提取AI图片")
        self.extract_ai_btn.clicked.connect(lambda: self.extract_images("generated"))
        web_controls.addWidget(self.extract_ai_btn)

        self.extract_latest_btn = QPushButton("提取最新图片")
        self.extract_latest_btn.clicked.connect(lambda: self.extract_images("latest"))
        web_controls.addWidget(self.extract_latest_btn)

        # 添加从剪贴板导入按钮
        self.import_clipboard_btn = QPushButton("从剪贴板导入")
        self.import_clipboard_btn.clicked.connect(self.import_from_clipboard)
        web_controls.addWidget(self.import_clipboard_btn)
        
        web_controls.addStretch()
        web_layout.addLayout(web_controls)
        
        # 创建安全的WebEngine视图
        try:
            self.web_view = create_safe_webview(self)
            if self.web_view is not None:
                # 先添加到布局，再设置URL
                web_layout.addWidget(self.web_view)

                # 延迟加载URL，确保WebEngine完全初始化
                from PySide6.QtCore import QTimer
                self.load_timer = QTimer()
                self.load_timer.setSingleShot(True)
                self.load_timer.timeout.connect(self._delayed_load_url)
                self.load_timer.start(500)  # 延迟500ms加载

                logging.info("WebEngine视图创建成功，准备加载URL")

                # 初始化图片提取器
                self.image_extractor = ImageExtractor(self.web_view)
                self.image_extractor.image_extracted.connect(self.on_image_extracted)
                self.image_extractor.extraction_progress.connect(self.on_extraction_progress)
                self.image_extractor.extraction_error.connect(self.on_extraction_error)
            else:
                # WebEngine创建失败，设置为None并显示说明
                self.web_view = None
                self.image_extractor = None
                self._add_webengine_fallback_info(web_layout)
        except Exception as e:
            logging.error(f"WebEngine创建异常: {e}")
            self.web_view = None
            self.image_extractor = None
            self._add_webengine_fallback_info(web_layout)
        
        self.tab_widget.addTab(web_tab, "豆包生图")
        
        # 生成结果标签页
        result_tab = QWidget()
        result_layout = QVBoxLayout(result_tab)
        
        # 结果控制按钮
        result_controls = QHBoxLayout()
        self.save_image_btn = QPushButton("保存图片")
        self.save_image_btn.clicked.connect(self.save_current_image)
        result_controls.addWidget(self.save_image_btn)

        self.copy_prompt_btn = QPushButton("复制提示词")
        self.copy_prompt_btn.clicked.connect(self.copy_prompt)
        result_controls.addWidget(self.copy_prompt_btn)

        # 添加图片导入按钮
        self.import_image_btn = QPushButton("导入图片")
        self.import_image_btn.clicked.connect(self.import_image_file)
        result_controls.addWidget(self.import_image_btn)

        self.paste_image_btn = QPushButton("粘贴图片")
        self.paste_image_btn.clicked.connect(self.paste_image_from_clipboard)
        result_controls.addWidget(self.paste_image_btn)
        
        result_controls.addStretch()
        result_layout.addLayout(result_controls)
        
        # 图片显示区域
        self.image_scroll = QScrollArea()
        self.image_display = QLabel()
        self.image_display.setAlignment(Qt.AlignCenter)
        self.image_display.setText("生成的图片将显示在这里")
        self.image_display.setStyleSheet("border: 1px solid gray; min-height: 400px;")
        self.image_scroll.setWidget(self.image_display)
        result_layout.addWidget(self.image_scroll)
        
        # 图片信息
        info_layout = QHBoxLayout()
        self.image_info_label = QLabel("图片信息: 无")
        info_layout.addWidget(self.image_info_label)
        info_layout.addStretch()
        result_layout.addLayout(info_layout)
        
        self.tab_widget.addTab(result_tab, "生成结果")
        
        # 图片库标签页
        gallery_tab = QWidget()
        gallery_layout = QVBoxLayout(gallery_tab)
        
        # 图片库控制
        gallery_controls = QHBoxLayout()
        self.clear_gallery_btn = QPushButton("清空图片库")
        self.clear_gallery_btn.clicked.connect(self.clear_gallery)
        gallery_controls.addWidget(self.clear_gallery_btn)
        
        self.export_gallery_btn = QPushButton("导出全部")
        self.export_gallery_btn.clicked.connect(self.export_all_images)
        gallery_controls.addWidget(self.export_gallery_btn)
        
        gallery_controls.addStretch()
        gallery_layout.addLayout(gallery_controls)
        
        # 图片库列表
        self.gallery_list = QListWidget()
        self.gallery_list.itemClicked.connect(self.view_gallery_image)
        gallery_layout.addWidget(self.gallery_list)
        
        self.tab_widget.addTab(gallery_tab, "图片库")
        
        layout.addWidget(self.tab_widget)

        return widget

    def import_storyboard(self):
        """导入分镜脚本"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入分镜脚本", "",
            "文本文件 (*.txt);;所有文件 (*.*)"
        )

        if file_path:
            success, content = self.text_processor.read_file(file_path)
            if success:
                self.script_input.setPlainText(content)
                self.statusBar().showMessage(f"已导入脚本: {Path(file_path).name}")
                # 自动解析脚本
                self.parse_storyboard()
            else:
                QMessageBox.warning(self, "错误", content)

    def parse_storyboard(self):
        """解析分镜脚本"""
        script_content = self.script_input.toPlainText()
        if not script_content.strip():
            QMessageBox.warning(self, "警告", "请先输入分镜脚本")
            return

        self.storyboard_scenes = []
        self.scene_list.clear()

        # 解析分镜脚本
        lines = script_content.split('\n')
        current_scene = {}

        for line in lines:
            line = line.strip()
            if line.startswith('【镜头') and '】' in line:
                # 保存上一个场景
                if current_scene:
                    self.storyboard_scenes.append(current_scene)

                # 开始新场景
                current_scene = {
                    'title': line,
                    'scene': '',
                    'composition': '',
                    'content': '',
                    'duration': '',
                    'notes': ''
                }
            elif line.startswith('场景：'):
                current_scene['scene'] = line[3:]
            elif line.startswith('构图：'):
                current_scene['composition'] = line[3:]
            elif line.startswith('内容：'):
                current_scene['content'] = line[3:]
            elif line.startswith('时长：'):
                current_scene['duration'] = line[3:]
            elif line.startswith('备注：'):
                current_scene['notes'] = line[3:]

        # 添加最后一个场景
        if current_scene:
            self.storyboard_scenes.append(current_scene)

        # 更新场景列表
        for i, scene in enumerate(self.storyboard_scenes):
            item = QListWidgetItem(f"{i+1}. {scene['title']}")
            item.setData(Qt.UserRole, i)
            self.scene_list.addItem(item)

        self.statusBar().showMessage(f"解析完成，共{len(self.storyboard_scenes)}个场景")

    def select_scene(self, item):
        """选择场景"""
        scene_index = item.data(Qt.UserRole)
        if scene_index < len(self.storyboard_scenes):
            scene = self.storyboard_scenes[scene_index]

            self.current_scene_label.setText(scene['title'])

            # 生成图片提示词
            prompt = self.generate_image_prompt(scene)
            self.scene_prompt.setPlainText(prompt)

    def generate_image_prompt(self, scene):
        """生成图片提示词"""
        prompt_parts = []

        # 场景描述
        if scene['scene']:
            prompt_parts.append(f"Scene: {scene['scene']}")

        # 构图
        if scene['composition']:
            prompt_parts.append(f"Composition: {scene['composition']}")

        # 内容描述
        if scene['content']:
            prompt_parts.append(f"Content: {scene['content']}")

        # 备注
        if scene['notes']:
            prompt_parts.append(f"Notes: {scene['notes']}")

        return ", ".join(prompt_parts)

    def optimize_prompt(self):
        """优化提示词"""
        current_prompt = self.scene_prompt.toPlainText()
        if not current_prompt.strip():
            QMessageBox.warning(self, "警告", "请先选择场景")
            return

        # 使用AI优化提示词
        optimization_prompt = f"""
请优化以下图片生成提示词，使其更适合AI图片生成：

原始提示词：
{current_prompt}

优化要求：
1. 使用英文关键词
2. 描述要具体生动
3. 包含画面构图信息
4. 添加艺术风格描述
5. 确保语法正确

请提供优化后的提示词：
"""

        try:
            optimized = self.ai_generator._call_ai_api(optimization_prompt, "prompt_optimization")
            self.scene_prompt.setPlainText(optimized)
            self.statusBar().showMessage("提示词优化完成")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"优化失败: {e}")

    def generate_single_image(self):
        """生成单张图片"""
        prompt = self.scene_prompt.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "警告", "请先选择场景或输入提示词")
            return

        style = self.style_combo.currentText()

        # 开始生成
        self.start_image_generation(prompt, style)

    def generate_all_images(self):
        """生成全部图片"""
        if not self.storyboard_scenes:
            QMessageBox.warning(self, "警告", "请先解析分镜脚本")
            return

        # 简化实现：逐个生成
        QMessageBox.information(self, "提示", "批量生成功能开发中，请使用单张生成")

    def start_image_generation(self, prompt, style):
        """开始图片生成"""
        # 禁用按钮
        self.set_buttons_enabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # 更新状态
        self.statusBar().showMessage("正在生成图片...")

        # 创建工作线程
        self.worker = ImageGenerationWorker(self.ai_generator, prompt, style)
        self.worker.finished.connect(self.on_image_generated)
        self.worker.error.connect(self.on_generation_error)
        self.worker.start()

    def on_image_generated(self, image_url):
        """图片生成完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.set_buttons_enabled(True)

        if image_url.startswith("错误") or image_url.startswith("图片生成失败"):
            QMessageBox.critical(self, "生成失败", image_url)
            self.statusBar().showMessage("图片生成失败")
            return

        # 显示图片
        try:
            # 更新图片显示
            self.display_generated_image(image_url)

            # 更新图片信息
            current_prompt = self.scene_prompt.toPlainText()
            style = self.style_combo.currentText()
            self.image_info_label.setText(f"风格: {style} | 提示词: {current_prompt[:50]}...")

            # 添加到图片库
            item = QListWidgetItem(f"图片 {self.gallery_list.count() + 1} - {style}")
            item.setData(Qt.UserRole, {
                'url': image_url,
                'prompt': current_prompt,
                'style': style,
                'timestamp': self.get_current_timestamp()
            })
            self.gallery_list.addItem(item)

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

            self.statusBar().showMessage("图片生成完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示图片失败: {e}")

    def on_generation_error(self, error_msg):
        """生成错误"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.set_buttons_enabled(True)

        # 显示错误
        QMessageBox.critical(self, "生成失败", f"图片生成失败:\n{error_msg}")
        self.statusBar().showMessage("生成失败")

    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.generate_single_btn.setEnabled(enabled)
        self.generate_all_btn.setEnabled(enabled)
        self.optimize_prompt_btn.setEnabled(enabled)

    def refresh_web(self):
        """刷新网页"""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                # 重置重试计数
                self.retry_count = 0

                # 先尝试重新加载
                self.web_view.reload()
                logging.info("网页刷新成功")
                self.statusBar().showMessage("正在刷新豆包网页...")

                # 如果重新加载失败，尝试重新设置URL
                from PySide6.QtCore import QTimer
                fallback_timer = QTimer()
                fallback_timer.setSingleShot(True)
                fallback_timer.timeout.connect(lambda: self._fallback_reload())
                fallback_timer.start(3000)  # 3秒后检查是否需要备用方案

            else:
                # WebEngine不可用，打开外部浏览器
                import webbrowser
                webbrowser.open("https://www.doubao.com/chat/")
                self.statusBar().showMessage("已在外部浏览器中打开豆包网站")
        except Exception as e:
            logging.error(f"刷新网页失败: {e}")
            # 备用方案：打开浏览器
            import webbrowser
            webbrowser.open("https://www.doubao.com/chat/")
            self.statusBar().showMessage("网页刷新失败，已在外部浏览器中打开")

    def go_home(self):
        """返回首页"""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                self.web_view.setUrl(QUrl("https://www.doubao.com/chat/"))
                logging.info("已返回豆包首页")
                self.statusBar().showMessage("已返回豆包首页")
            else:
                # WebEngine不可用，打开外部浏览器
                import webbrowser
                webbrowser.open("https://www.doubao.com/chat/")
                self.statusBar().showMessage("已在外部浏览器中打开豆包网站")
        except Exception as e:
            logging.error(f"返回首页失败: {e}")
            # 备用方案：打开浏览器
            import webbrowser
            webbrowser.open("https://www.doubao.com/chat/")
            self.statusBar().showMessage("返回首页失败，已在外部浏览器中打开")





    def view_gallery_image(self, item):
        """查看图片库中的图片"""
        image_data = item.data(Qt.UserRole)
        if image_data:
            if isinstance(image_data, dict):
                # 新格式的数据
                image_url = image_data.get('url', '')
                prompt = image_data.get('prompt', '')
                style = image_data.get('style', '')
                timestamp = image_data.get('timestamp', '')

                # 显示图片
                self.display_generated_image(image_url)

                # 更新信息
                self.image_info_label.setText(
                    f"风格: {style} | 时间: {timestamp} | 提示词: {prompt[:30]}..."
                )

                # 更新提示词显示
                self.scene_prompt.setPlainText(prompt)
            else:
                # 兼容旧格式
                self.display_generated_image(image_data)
                self.image_info_label.setText("图片信息: 旧格式数据")

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

    def clear_gallery(self):
        """清空图片库"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空图片库吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.gallery_list.clear()
            self.statusBar().showMessage("图片库已清空")

    def export_images(self):
        """导出图片"""
        if self.gallery_list.count() == 0:
            QMessageBox.warning(self, "警告", "图片库为空")
            return

        # 选择导出目录
        export_dir = QFileDialog.getExistingDirectory(self, "选择导出目录")
        if export_dir:
            # 简化实现
            QMessageBox.information(self, "提示", f"将导出到: {export_dir}")

    def export_all_images(self):
        """导出全部图片"""
        self.export_images()

    def batch_generate(self):
        """批量生成"""
        QMessageBox.information(self, "提示", "批量生成功能开发中")

    def display_generated_image(self, image_url):
        """显示生成的图片"""
        try:
            if image_url.startswith('http'):
                # 网络图片，尝试下载并显示
                self.download_and_display_image(image_url)
            else:
                # 如果是本地文件路径
                if os.path.exists(image_url):
                    pixmap = QPixmap(image_url)
                    if not pixmap.isNull():
                        # 缩放图片以适应显示区域
                        scaled_pixmap = pixmap.scaled(
                            600, 400,
                            Qt.KeepAspectRatio,
                            Qt.SmoothTransformation
                        )
                        self.image_display.setPixmap(scaled_pixmap)
                        self.current_image_url = image_url
                        logging.info(f"本地图片显示成功: {image_url}")
                    else:
                        self.image_display.setText("图片加载失败")
                        logging.error(f"本地图片加载失败: {image_url}")
                else:
                    self.image_display.setText(f"图片文件不存在: {image_url}")
                    logging.error(f"图片文件不存在: {image_url}")
        except Exception as e:
            logging.error(f"显示图片失败: {e}")
            self.image_display.setText(f"图片显示错误: {e}")

    def download_and_display_image(self, image_url):
        """下载网络图片并显示"""
        try:
            import requests
            from PySide6.QtCore import QThread, Signal

            # 显示下载状态
            self.image_display.setText(f"""
            <div style='text-align: center; padding: 20px;'>
                <h3>正在下载图片...</h3>
                <p>图片URL: {image_url}</p>
                <p>请稍候...</p>
            </div>
            """)

            # 创建下载线程
            self.download_worker = ImageDownloadWorker(image_url)
            self.download_worker.download_finished.connect(self.on_image_downloaded)
            self.download_worker.download_error.connect(self.on_download_error)
            self.download_worker.start()

        except Exception as e:
            logging.error(f"启动图片下载失败: {e}")
            self.show_image_url_fallback(image_url)

    def on_image_downloaded(self, local_path):
        """图片下载完成"""
        try:
            if os.path.exists(local_path):
                pixmap = QPixmap(local_path)
                if not pixmap.isNull():
                    # 缩放图片以适应显示区域
                    scaled_pixmap = pixmap.scaled(
                        600, 400,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.image_display.setPixmap(scaled_pixmap)
                    self.current_image_url = local_path
                    self.statusBar().showMessage("图片下载并显示成功")
                    logging.info(f"网络图片下载并显示成功: {local_path}")
                else:
                    self.image_display.setText("下载的图片无效")
                    logging.error(f"下载的图片无效: {local_path}")
            else:
                self.image_display.setText("下载的图片文件不存在")
                logging.error(f"下载的图片文件不存在: {local_path}")
        except Exception as e:
            logging.error(f"显示下载的图片失败: {e}")
            self.image_display.setText(f"显示下载图片失败: {e}")

    def on_download_error(self, error_message, original_url):
        """图片下载失败"""
        logging.error(f"图片下载失败: {error_message}")
        self.statusBar().showMessage(f"图片下载失败: {error_message}")
        # 显示备用方案
        self.show_image_url_fallback(original_url)

    def show_image_url_fallback(self, image_url):
        """显示图片URL备用方案"""
        self.image_display.setText(f"""
        <div style='text-align: center; padding: 20px;'>
            <h3>图片提取成功！</h3>
            <p><b>图片链接：</b></p>
            <p style='word-break: break-all; color: blue;'>{image_url}</p>
            <br>
            <p><b>使用说明：</b></p>
            <p>1. 复制上方链接到浏览器打开</p>
            <p>2. 右键保存图片到本地</p>
            <p>3. 使用"导入图片"功能导入保存的文件</p>
            <br>
            <p style='color: orange;'><b>注意：</b>自动下载失败，请手动保存</p>
        </div>
        """)
        self.current_image_url = image_url

    def get_current_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def save_current_image(self):
        """保存当前图片"""
        if not hasattr(self, 'current_image_url') or not self.current_image_url:
            QMessageBox.warning(self, "警告", "没有图片可保存")
            return

        try:
            if self.current_image_url.startswith('http'):
                # 网络图片，提供下载链接
                reply = QMessageBox.question(
                    self, "保存图片",
                    f"图片链接：{self.current_image_url}\n\n"
                    "请选择保存方式：\n"
                    "是(Yes) - 复制链接到剪贴板\n"
                    "否(No) - 在浏览器中打开",
                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
                )

                if reply == QMessageBox.Yes:
                    # 复制链接到剪贴板
                    from PySide6.QtWidgets import QApplication
                    clipboard = QApplication.clipboard()
                    clipboard.setText(self.current_image_url)
                    QMessageBox.information(self, "成功", "图片链接已复制到剪贴板")
                elif reply == QMessageBox.No:
                    # 在浏览器中打开
                    import webbrowser
                    webbrowser.open(self.current_image_url)
            else:
                # 本地图片，复制到指定位置
                save_path, _ = QFileDialog.getSaveFileName(
                    self, "保存图片", "",
                    "图片文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*.*)"
                )
                if save_path:
                    import shutil
                    shutil.copy2(self.current_image_url, save_path)
                    QMessageBox.information(self, "成功", f"图片已保存到: {save_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存图片失败: {e}")

    def copy_prompt(self):
        """复制提示词"""
        prompt = self.scene_prompt.toPlainText()
        if prompt:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(prompt)
            self.statusBar().showMessage("提示词已复制到剪贴板")
        else:
            QMessageBox.warning(self, "警告", "没有提示词可复制")

    def test_generate_image(self):
        """测试图片生成功能"""
        # 模拟图片生成成功
        test_url = "https://example.com/test-image.jpg"
        test_prompt = "测试提示词: 一个美丽的风景画，包含山川河流，艺术风格"

        # 设置测试提示词
        self.scene_prompt.setPlainText(test_prompt)

        # 模拟生成完成
        self.display_generated_image(test_url)

        # 更新图片信息
        style = self.style_combo.currentText()
        self.image_info_label.setText(f"风格: {style} | 测试图片 | 提示词: {test_prompt[:30]}...")

        # 添加到图片库
        item = QListWidgetItem(f"测试图片 {self.gallery_list.count() + 1} - {style}")
        item.setData(Qt.UserRole, {
            'url': test_url,
            'prompt': test_prompt,
            'style': style,
            'timestamp': self.get_current_timestamp()
        })
        self.gallery_list.addItem(item)

        # 切换到结果标签页
        self.tab_widget.setCurrentIndex(1)

        self.statusBar().showMessage("测试图片生成完成")

        QMessageBox.information(
            self, "测试完成",
            "测试图片已生成！\n\n"
            "功能说明：\n"
            "1. 生成结果标签页显示图片信息\n"
            "2. 可以保存图片链接\n"
            "3. 可以复制提示词\n"
            "4. 图片库记录所有生成的图片"
        )

    def _add_webengine_fallback_info(self, layout):
        """添加WebEngine备用信息"""
        web_info = QTextEdit()
        web_info.setReadOnly(True)
        web_info.setHtml("""
        <div style='padding: 20px; text-align: center;'>
            <h3>🌐 豆包生图使用说明</h3>
            <p style='color: orange;'><b>WebEngine组件不可用，请使用外部浏览器</b></p>

            <h4>📋 使用步骤：</h4>
            <ol style='text-align: left; max-width: 500px; margin: 0 auto;'>
                <li>点击下方"刷新"或"返回首页"按钮打开豆包网站</li>
                <li>在网页中输入生成的图片提示词</li>
                <li>添加"请生成图片"或"画一张图"等指令</li>
                <li>等待豆包生成图片</li>
                <li><b>保存图片的方法：</b></li>
                <ul>
                    <li>右键保存图片到本地，然后使用"导入图片"按钮</li>
                    <li>复制图片，然后使用"从剪贴板导入"按钮</li>
                    <li>在生成结果页面使用"粘贴图片"功能</li>
                </ul>
            </ol>

            <h4>💡 提示词示例：</h4>
            <p style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>
                请根据以下描述生成一张图片：一个古装美女推门而入，中景构图，古代房间背景，写实风格
            </p>

            <h4>🔧 解决方案：</h4>
            <p>如需内嵌网页功能，请运行 <code>python diagnose_webengine.py</code> 进行诊断</p>
        </div>
        """)
        layout.addWidget(web_info)



    def import_from_clipboard(self):
        """从剪贴板导入图片"""
        try:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()

            # 检查剪贴板中是否有图片
            if clipboard.mimeData().hasImage():
                pixmap = clipboard.pixmap()
                if not pixmap.isNull():
                    # 保存剪贴板图片到临时文件
                    import tempfile
                    temp_dir = tempfile.gettempdir()
                    timestamp = self.get_current_timestamp().replace(":", "-").replace(" ", "_")
                    temp_file = os.path.join(temp_dir, f"clipboard_image_{timestamp}.png")

                    if pixmap.save(temp_file, "PNG"):
                        self.add_image_to_gallery(temp_file, "剪贴板图片", "clipboard")
                        QMessageBox.information(self, "成功", "剪贴板图片已导入")
                    else:
                        QMessageBox.warning(self, "错误", "保存剪贴板图片失败")
                else:
                    QMessageBox.warning(self, "警告", "剪贴板中的图片无效")
            else:
                QMessageBox.information(self, "提示", "剪贴板中没有图片\n\n请先复制图片到剪贴板")
        except Exception as e:
            logging.error(f"从剪贴板导入失败: {e}")
            QMessageBox.critical(self, "错误", f"导入失败: {e}")

    def import_image_file(self):
        """导入图片文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片文件", "",
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.webp);;所有文件 (*.*)"
        )

        if file_path:
            try:
                # 验证图片文件
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    self.add_image_to_gallery(file_path, "导入图片", "imported")
                    QMessageBox.information(self, "成功", f"图片已导入\n\n文件: {os.path.basename(file_path)}")
                else:
                    QMessageBox.warning(self, "错误", "无效的图片文件")
            except Exception as e:
                logging.error(f"导入图片文件失败: {e}")
                QMessageBox.critical(self, "错误", f"导入失败: {e}")

    def paste_image_from_clipboard(self):
        """从剪贴板粘贴图片到结果显示"""
        try:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()

            if clipboard.mimeData().hasImage():
                pixmap = clipboard.pixmap()
                if not pixmap.isNull():
                    # 直接显示剪贴板图片
                    scaled_pixmap = pixmap.scaled(
                        600, 400,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.image_display.setPixmap(scaled_pixmap)

                    # 保存到临时文件以便后续保存
                    import tempfile
                    temp_dir = tempfile.gettempdir()
                    timestamp = self.get_current_timestamp().replace(":", "-").replace(" ", "_")
                    temp_file = os.path.join(temp_dir, f"pasted_image_{timestamp}.png")

                    if pixmap.save(temp_file, "PNG"):
                        self.current_image_url = temp_file

                        # 更新图片信息
                        current_prompt = self.scene_prompt.toPlainText() or "粘贴的图片"
                        self.image_info_label.setText(f"类型: 粘贴图片 | 提示词: {current_prompt[:30]}...")

                        self.statusBar().showMessage("图片已粘贴到结果显示区域")
                    else:
                        QMessageBox.warning(self, "警告", "保存临时文件失败")
                else:
                    QMessageBox.warning(self, "警告", "剪贴板中的图片无效")
            else:
                QMessageBox.information(self, "提示", "剪贴板中没有图片")
        except Exception as e:
            logging.error(f"粘贴图片失败: {e}")
            QMessageBox.critical(self, "错误", f"粘贴失败: {e}")

    def add_image_to_gallery(self, image_path, image_type, source_type):
        """添加图片到图片库"""
        try:
            # 显示图片
            self.display_generated_image(image_path)

            # 获取当前提示词
            current_prompt = self.scene_prompt.toPlainText() or f"{image_type}，无提示词"
            style = self.style_combo.currentText()

            # 更新图片信息
            self.image_info_label.setText(f"类型: {image_type} | 风格: {style} | 提示词: {current_prompt[:30]}...")

            # 添加到图片库
            item = QListWidgetItem(f"{image_type} {self.gallery_list.count() + 1} - {style}")
            item.setData(Qt.UserRole, {
                'url': image_path,
                'prompt': current_prompt,
                'style': style,
                'timestamp': self.get_current_timestamp(),
                'type': source_type
            })
            self.gallery_list.addItem(item)

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

            self.statusBar().showMessage(f"{image_type}已添加到图片库")

        except Exception as e:
            logging.error(f"添加图片到图片库失败: {e}")
            QMessageBox.critical(self, "错误", f"添加失败: {e}")

    def _delayed_load_url(self):
        """延迟加载豆包网页URL"""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                self.web_view.setUrl(QUrl("https://www.doubao.com/chat/"))
                logging.info("豆包网页URL加载完成")
                self.statusBar().showMessage("豆包网页加载中...")

                # 监听页面加载完成
                if hasattr(self.web_view, 'loadFinished'):
                    self.web_view.loadFinished.connect(self._on_page_loaded)
            else:
                logging.warning("WebEngine视图不可用，无法加载URL")
        except Exception as e:
            logging.error(f"延迟加载URL失败: {e}")
            # 如果延迟加载也失败，提供重试机制
            self._setup_retry_mechanism()

    def _on_page_loaded(self, success):
        """页面加载完成回调"""
        try:
            if success:
                logging.info("豆包网页加载成功")
                self.statusBar().showMessage("豆包网页加载完成")
            else:
                logging.warning("豆包网页加载失败")
                self.statusBar().showMessage("豆包网页加载失败，请点击刷新重试")
                # 自动重试一次
                self._setup_retry_mechanism()
        except Exception as e:
            logging.error(f"页面加载回调异常: {e}")

    def _setup_retry_mechanism(self):
        """设置重试机制"""
        try:
            if not hasattr(self, 'retry_count'):
                self.retry_count = 0

            if self.retry_count < 2:  # 最多重试2次
                self.retry_count += 1
                logging.info(f"准备第{self.retry_count}次重试加载豆包网页")

                # 延迟重试
                from PySide6.QtCore import QTimer
                retry_timer = QTimer()
                retry_timer.setSingleShot(True)
                retry_timer.timeout.connect(self._retry_load_url)
                retry_timer.start(2000)  # 延迟2秒重试
            else:
                logging.warning("豆包网页加载重试次数已达上限")
                self.statusBar().showMessage("豆包网页加载失败，请手动点击刷新")
        except Exception as e:
            logging.error(f"设置重试机制失败: {e}")

    def _retry_load_url(self):
        """重试加载URL"""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                logging.info(f"执行第{self.retry_count}次重试")
                self.web_view.setUrl(QUrl("https://www.doubao.com/chat/"))
                self.statusBar().showMessage(f"正在重试加载豆包网页（第{self.retry_count}次）...")
        except Exception as e:
            logging.error(f"重试加载URL失败: {e}")

    def _fallback_reload(self):
        """备用重载方法"""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                # 尝试重新设置URL
                logging.info("执行备用重载方案")
                self.web_view.setUrl(QUrl("https://www.doubao.com/chat/"))
                self.statusBar().showMessage("正在尝试重新加载豆包网页...")
        except Exception as e:
            logging.error(f"备用重载失败: {e}")

    def extract_images(self, extraction_type):
        """高级图片提取"""
        if not hasattr(self, 'image_extractor') or self.image_extractor is None:
            QMessageBox.information(
                self, "提示",
                "图片提取功能需要WebEngine支持。\n\n"
                "请使用以下替代方案：\n"
                "1. 在外部浏览器中保存豆包生成的图片\n"
                "2. 使用'导入图片'或'从剪贴板导入'功能\n"
                "3. 运行 python diagnose_webengine.py 检查WebEngine状态"
            )
            return

        try:
            # 禁用提取按钮，防止重复操作
            self.set_extraction_buttons_enabled(False)

            # 开始提取
            self.image_extractor.extract_images_from_page(extraction_type)

            # 显示提取类型
            type_names = {
                "all": "所有图片",
                "visible": "可见图片",
                "generated": "AI生成图片",
                "latest": "最新图片"
            }

            self.statusBar().showMessage(f"正在提取{type_names.get(extraction_type, extraction_type)}...")

        except Exception as e:
            logging.error(f"启动图片提取失败: {e}")
            QMessageBox.critical(self, "错误", f"启动图片提取失败: {e}")
            self.set_extraction_buttons_enabled(True)

    def set_extraction_buttons_enabled(self, enabled):
        """设置提取按钮的启用状态"""
        if hasattr(self, 'extract_all_btn'):
            self.extract_all_btn.setEnabled(enabled)
        if hasattr(self, 'extract_ai_btn'):
            self.extract_ai_btn.setEnabled(enabled)
        if hasattr(self, 'extract_latest_btn'):
            self.extract_latest_btn.setEnabled(enabled)

    def on_image_extracted(self, image_path, image_info):
        """图片提取完成回调"""
        try:
            # 显示提取的图片
            self.display_generated_image(image_path)

            # 更新图片信息
            extraction_method = image_info.get('extraction_method', '未知')
            image_size = f"{image_info.get('width', 0)}x{image_info.get('height', 0)}"

            info_text = f"提取方式: {extraction_method} | 尺寸: {image_size}"
            if image_info.get('aiGenerated'):
                info_text += " | AI生成图片"
            if image_info.get('isLatest'):
                info_text += " | 最新图片"

            self.image_info_label.setText(info_text)

            # 添加到图片库
            current_prompt = self.scene_prompt.toPlainText() or "网页提取图片"

            item_name = f"提取图片 {self.gallery_list.count() + 1}"
            if image_info.get('aiGenerated'):
                item_name += " (AI生成)"
            if image_info.get('isLatest'):
                item_name += " (最新)"

            item = QListWidgetItem(item_name)
            item.setData(Qt.UserRole, {
                'url': image_path,
                'prompt': current_prompt,
                'style': '网页提取',
                'timestamp': self.get_current_timestamp(),
                'type': 'extracted',
                'extraction_info': image_info
            })
            self.gallery_list.addItem(item)

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

            logging.info(f"图片提取成功: {image_path}")

        except Exception as e:
            logging.error(f"处理提取的图片失败: {e}")

    def on_extraction_progress(self, progress, message):
        """提取进度更新回调"""
        self.statusBar().showMessage(f"{message} ({progress}%)")

        # 如果有进度条，更新进度条
        if hasattr(self, 'progress_bar') and self.progress_bar:
            self.progress_bar.setValue(progress)

    def on_extraction_error(self, error_message):
        """提取错误回调"""
        logging.error(f"图片提取错误: {error_message}")
        self.statusBar().showMessage(f"图片提取失败: {error_message}")

        # 重新启用按钮
        self.set_extraction_buttons_enabled(True)

        # 显示错误对话框
        QMessageBox.warning(self, "图片提取失败", f"提取失败: {error_message}\n\n请尝试以下解决方案：\n1. 刷新网页后重试\n2. 使用其他提取方式\n3. 手动保存图片后导入")

    def import_from_clipboard(self):
        """从剪贴板导入图片"""
        try:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()

            # 检查剪贴板中是否有图片
            if clipboard.mimeData().hasImage():
                pixmap = clipboard.pixmap()
                if not pixmap.isNull():
                    # 保存剪贴板图片到临时文件
                    import tempfile
                    temp_dir = tempfile.gettempdir()
                    timestamp = self.get_current_timestamp().replace(":", "-").replace(" ", "_")
                    temp_file = os.path.join(temp_dir, f"clipboard_image_{timestamp}.png")

                    if pixmap.save(temp_file, "PNG"):
                        self.add_image_to_gallery(temp_file, "剪贴板图片", "clipboard")
                        QMessageBox.information(self, "成功", "剪贴板图片已导入")
                    else:
                        QMessageBox.warning(self, "错误", "保存剪贴板图片失败")
                else:
                    QMessageBox.warning(self, "警告", "剪贴板中的图片无效")
            else:
                QMessageBox.information(self, "提示", "剪贴板中没有图片\n\n请先复制图片到剪贴板")
        except Exception as e:
            logging.error(f"从剪贴板导入失败: {e}")
            QMessageBox.critical(self, "错误", f"导入失败: {e}")

    def import_image_file(self):
        """导入图片文件"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "选择图片文件", "",
            "图片文件 (*.png *.jpg *.jpeg *.bmp *.gif *.webp);;所有文件 (*.*)"
        )

        if file_path:
            try:
                # 验证图片文件
                pixmap = QPixmap(file_path)
                if not pixmap.isNull():
                    self.add_image_to_gallery(file_path, "导入图片", "imported")
                    QMessageBox.information(self, "成功", f"图片已导入\n\n文件: {os.path.basename(file_path)}")
                else:
                    QMessageBox.warning(self, "错误", "无效的图片文件")
            except Exception as e:
                logging.error(f"导入图片文件失败: {e}")
                QMessageBox.critical(self, "错误", f"导入失败: {e}")

    def paste_image_from_clipboard(self):
        """从剪贴板粘贴图片到结果显示"""
        try:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()

            if clipboard.mimeData().hasImage():
                pixmap = clipboard.pixmap()
                if not pixmap.isNull():
                    # 直接显示剪贴板图片
                    scaled_pixmap = pixmap.scaled(
                        600, 400,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.image_display.setPixmap(scaled_pixmap)

                    # 保存到临时文件以便后续保存
                    import tempfile
                    temp_dir = tempfile.gettempdir()
                    timestamp = self.get_current_timestamp().replace(":", "-").replace(" ", "_")
                    temp_file = os.path.join(temp_dir, f"pasted_image_{timestamp}.png")

                    if pixmap.save(temp_file, "PNG"):
                        self.current_image_url = temp_file

                        # 更新图片信息
                        current_prompt = self.scene_prompt.toPlainText() or "粘贴的图片"
                        self.image_info_label.setText(f"类型: 粘贴图片 | 提示词: {current_prompt[:30]}...")

                        self.statusBar().showMessage("图片已粘贴到结果显示区域")
                    else:
                        QMessageBox.warning(self, "警告", "保存临时文件失败")
                else:
                    QMessageBox.warning(self, "警告", "剪贴板中的图片无效")
            else:
                QMessageBox.information(self, "提示", "剪贴板中没有图片")
        except Exception as e:
            logging.error(f"粘贴图片失败: {e}")
            QMessageBox.critical(self, "错误", f"粘贴失败: {e}")

    def add_image_to_gallery(self, image_path, image_type, source_type):
        """添加图片到图片库"""
        try:
            # 显示图片
            self.display_generated_image(image_path)

            # 获取当前提示词
            current_prompt = self.scene_prompt.toPlainText() or f"{image_type}，无提示词"
            style = self.style_combo.currentText()

            # 更新图片信息
            self.image_info_label.setText(f"类型: {image_type} | 风格: {style} | 提示词: {current_prompt[:30]}...")

            # 添加到图片库
            item = QListWidgetItem(f"{image_type} {self.gallery_list.count() + 1} - {style}")
            item.setData(Qt.UserRole, {
                'url': image_path,
                'prompt': current_prompt,
                'style': style,
                'timestamp': self.get_current_timestamp(),
                'type': source_type
            })
            self.gallery_list.addItem(item)

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

            self.statusBar().showMessage(f"{image_type}已添加到图片库")

        except Exception as e:
            logging.error(f"添加图片到图片库失败: {e}")
            QMessageBox.critical(self, "错误", f"添加失败: {e}")

    def copy_prompt(self):
        """复制提示词"""
        prompt = self.scene_prompt.toPlainText()
        if prompt:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(prompt)
            self.statusBar().showMessage("提示词已复制到剪贴板")
        else:
            QMessageBox.warning(self, "警告", "没有提示词可复制")
