"""
分镜图片生成窗口
内嵌网页支持豆包生图功能
"""

import os
import json
import logging
from pathlib import Path
from PySide6.QtWidgets import (
    QMainWindow, QWidget, QVBoxLayout, QHBoxLayout,
    QTextEdit, QPushButton, QLabel, QComboBox, QTabWidget,
    QFileDialog, QMessageBox, QProgressBar, QSplitter,
    QGroupBox, QGridLayout, QLineEdit, QListWidget,
    QListWidgetItem, QScrollArea
)
from PySide6.QtCore import Qt, QThread, Signal, QUrl
from PySide6.QtGui import QFont, QAction, QPixmap

from core.ai_generator import AIGenerator
from core.text_processor import TextProcessor
from core.webengine_setup import create_safe_webview

class ImageGenerationWorker(QThread):
    """图片生成工作线程"""
    
    finished = Signal(str)  # 图片URL
    error = Signal(str)
    
    def __init__(self, ai_generator, prompt, style):
        super().__init__()
        self.ai_generator = ai_generator
        self.prompt = prompt
        self.style = style
    
    def run(self):
        try:
            result = self.ai_generator.generate_image_with_doubao(self.prompt, self.style)
            self.finished.emit(result)
        except Exception as e:
            self.error.emit(str(e))

class ImageGeneratorWindow(QMainWindow):
    """分镜图片生成主窗口"""
    
    def __init__(self):
        super().__init__()
        self.ai_generator = AIGenerator()
        self.text_processor = TextProcessor()
        self.worker = None
        self.storyboard_scenes = []
        
        self.init_ui()
        
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("分镜图片生成器")
        self.setGeometry(100, 100, 1600, 1000)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        
        # 主布局
        main_layout = QHBoxLayout(central_widget)
        
        # 创建分割器
        splitter = QSplitter(Qt.Horizontal)
        main_layout.addWidget(splitter)
        
        # 左侧控制区域
        left_widget = self.create_control_area()
        splitter.addWidget(left_widget)
        
        # 右侧显示区域
        right_widget = self.create_display_area()
        splitter.addWidget(right_widget)
        
        # 设置分割比例
        splitter.setSizes([500, 1100])
        
        # 状态栏
        self.statusBar().showMessage("分镜图片生成器已就绪")
        
        # 进度条
        self.progress_bar = QProgressBar()
        self.progress_bar.setVisible(False)
        self.statusBar().addPermanentWidget(self.progress_bar)
        
        # 创建菜单栏
        self.create_menu_bar()
    
    def create_menu_bar(self):
        """创建菜单栏"""
        menubar = self.menuBar()
        
        # 文件菜单
        file_menu = menubar.addMenu("文件")
        
        import_action = QAction("导入分镜脚本", self)
        import_action.triggered.connect(self.import_storyboard)
        file_menu.addAction(import_action)
        
        export_action = QAction("导出图片", self)
        export_action.triggered.connect(self.export_images)
        file_menu.addAction(export_action)
        
        # 工具菜单
        tools_menu = menubar.addMenu("工具")
        
        batch_generate_action = QAction("批量生成", self)
        batch_generate_action.triggered.connect(self.batch_generate)
        tools_menu.addAction(batch_generate_action)
    
    def create_control_area(self):
        """创建控制区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 分镜脚本输入组
        script_group = QGroupBox("分镜脚本")
        script_layout = QVBoxLayout(script_group)
        
        # 导入按钮
        import_layout = QHBoxLayout()
        self.import_script_btn = QPushButton("导入脚本")
        self.import_script_btn.clicked.connect(self.import_storyboard)
        import_layout.addWidget(self.import_script_btn)
        
        self.parse_script_btn = QPushButton("解析脚本")
        self.parse_script_btn.clicked.connect(self.parse_storyboard)
        import_layout.addWidget(self.parse_script_btn)
        import_layout.addStretch()
        script_layout.addLayout(import_layout)
        
        # 脚本内容
        self.script_input = QTextEdit()
        self.script_input.setPlaceholderText("请输入或导入分镜脚本...")
        self.script_input.setMaximumHeight(200)
        script_layout.addWidget(self.script_input)
        
        layout.addWidget(script_group)
        
        # 生成设置组
        settings_group = QGroupBox("生成设置")
        settings_layout = QGridLayout(settings_group)
        
        settings_layout.addWidget(QLabel("图片风格:"), 0, 0)
        self.style_combo = QComboBox()
        self.style_combo.addItems([
            "realistic", "anime", "cartoon", "sketch", 
            "oil_painting", "watercolor", "digital_art", "3d_render"
        ])
        settings_layout.addWidget(self.style_combo, 0, 1)
        
        settings_layout.addWidget(QLabel("图片尺寸:"), 1, 0)
        self.size_combo = QComboBox()
        self.size_combo.addItems(["1024x1024", "1024x768", "768x1024", "1280x720"])
        settings_layout.addWidget(self.size_combo, 1, 1)
        
        layout.addWidget(settings_group)
        
        # 场景列表组
        scenes_group = QGroupBox("分镜场景")
        scenes_layout = QVBoxLayout(scenes_group)
        
        self.scene_list = QListWidget()
        self.scene_list.itemClicked.connect(self.select_scene)
        scenes_layout.addWidget(self.scene_list)
        
        # 场景操作按钮
        scene_buttons = QHBoxLayout()
        self.generate_single_btn = QPushButton("生成当前")
        self.generate_single_btn.clicked.connect(self.generate_single_image)
        scene_buttons.addWidget(self.generate_single_btn)
        
        self.generate_all_btn = QPushButton("生成全部")
        self.generate_all_btn.clicked.connect(self.generate_all_images)
        scene_buttons.addWidget(self.generate_all_btn)
        scenes_layout.addLayout(scene_buttons)
        
        layout.addWidget(scenes_group)
        
        # 当前场景详情
        current_group = QGroupBox("当前场景")
        current_layout = QVBoxLayout(current_group)
        
        self.current_scene_label = QLabel("未选择场景")
        current_layout.addWidget(self.current_scene_label)
        
        self.scene_prompt = QTextEdit()
        self.scene_prompt.setPlaceholderText("场景描述将显示在这里...")
        self.scene_prompt.setMaximumHeight(100)
        current_layout.addWidget(self.scene_prompt)
        
        # 提示词优化
        prompt_buttons = QHBoxLayout()
        self.optimize_prompt_btn = QPushButton("优化提示词")
        self.optimize_prompt_btn.clicked.connect(self.optimize_prompt)
        prompt_buttons.addWidget(self.optimize_prompt_btn)

        # 添加测试按钮
        self.test_generate_btn = QPushButton("测试生成")
        self.test_generate_btn.clicked.connect(self.test_generate_image)
        prompt_buttons.addWidget(self.test_generate_btn)

        prompt_buttons.addStretch()
        current_layout.addLayout(prompt_buttons)
        
        layout.addWidget(current_group)
        
        layout.addStretch()
        
        return widget
    
    def create_display_area(self):
        """创建显示区域"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 标签页
        self.tab_widget = QTabWidget()
        
        # 豆包生图网页标签页
        web_tab = QWidget()
        web_layout = QVBoxLayout(web_tab)
        
        # 网页控制按钮
        web_controls = QHBoxLayout()
        self.refresh_btn = QPushButton("刷新")
        self.refresh_btn.clicked.connect(self.refresh_web)
        web_controls.addWidget(self.refresh_btn)
        
        self.home_btn = QPushButton("返回首页")
        self.home_btn.clicked.connect(self.go_home)
        web_controls.addWidget(self.home_btn)
        
        web_controls.addStretch()
        web_layout.addLayout(web_controls)
        
        # 创建安全的WebEngine视图
        try:
            self.web_view = create_safe_webview(self)
            if self.web_view is not None:
                self.web_view.setUrl(QUrl("https://www.doubao.com/chat/"))
                web_layout.addWidget(self.web_view)
                logging.info("WebEngine视图创建成功")
            else:
                # WebEngine创建失败，设置为None并显示说明
                self.web_view = None
                self._add_webengine_fallback_info(web_layout)
        except Exception as e:
            logging.error(f"WebEngine创建异常: {e}")
            self.web_view = None
            self._add_webengine_fallback_info(web_layout)
        
        self.tab_widget.addTab(web_tab, "豆包生图")
        
        # 生成结果标签页
        result_tab = QWidget()
        result_layout = QVBoxLayout(result_tab)
        
        # 结果控制按钮
        result_controls = QHBoxLayout()
        self.save_image_btn = QPushButton("保存图片")
        self.save_image_btn.clicked.connect(self.save_current_image)
        result_controls.addWidget(self.save_image_btn)
        
        self.copy_prompt_btn = QPushButton("复制提示词")
        self.copy_prompt_btn.clicked.connect(self.copy_prompt)
        result_controls.addWidget(self.copy_prompt_btn)
        
        result_controls.addStretch()
        result_layout.addLayout(result_controls)
        
        # 图片显示区域
        self.image_scroll = QScrollArea()
        self.image_display = QLabel()
        self.image_display.setAlignment(Qt.AlignCenter)
        self.image_display.setText("生成的图片将显示在这里")
        self.image_display.setStyleSheet("border: 1px solid gray; min-height: 400px;")
        self.image_scroll.setWidget(self.image_display)
        result_layout.addWidget(self.image_scroll)
        
        # 图片信息
        info_layout = QHBoxLayout()
        self.image_info_label = QLabel("图片信息: 无")
        info_layout.addWidget(self.image_info_label)
        info_layout.addStretch()
        result_layout.addLayout(info_layout)
        
        self.tab_widget.addTab(result_tab, "生成结果")
        
        # 图片库标签页
        gallery_tab = QWidget()
        gallery_layout = QVBoxLayout(gallery_tab)
        
        # 图片库控制
        gallery_controls = QHBoxLayout()
        self.clear_gallery_btn = QPushButton("清空图片库")
        self.clear_gallery_btn.clicked.connect(self.clear_gallery)
        gallery_controls.addWidget(self.clear_gallery_btn)
        
        self.export_gallery_btn = QPushButton("导出全部")
        self.export_gallery_btn.clicked.connect(self.export_all_images)
        gallery_controls.addWidget(self.export_gallery_btn)
        
        gallery_controls.addStretch()
        gallery_layout.addLayout(gallery_controls)
        
        # 图片库列表
        self.gallery_list = QListWidget()
        self.gallery_list.itemClicked.connect(self.view_gallery_image)
        gallery_layout.addWidget(self.gallery_list)
        
        self.tab_widget.addTab(gallery_tab, "图片库")
        
        layout.addWidget(self.tab_widget)

        return widget

    def import_storyboard(self):
        """导入分镜脚本"""
        file_path, _ = QFileDialog.getOpenFileName(
            self, "导入分镜脚本", "",
            "文本文件 (*.txt);;所有文件 (*.*)"
        )

        if file_path:
            success, content = self.text_processor.read_file(file_path)
            if success:
                self.script_input.setPlainText(content)
                self.statusBar().showMessage(f"已导入脚本: {Path(file_path).name}")
                # 自动解析脚本
                self.parse_storyboard()
            else:
                QMessageBox.warning(self, "错误", content)

    def parse_storyboard(self):
        """解析分镜脚本"""
        script_content = self.script_input.toPlainText()
        if not script_content.strip():
            QMessageBox.warning(self, "警告", "请先输入分镜脚本")
            return

        self.storyboard_scenes = []
        self.scene_list.clear()

        # 解析分镜脚本
        lines = script_content.split('\n')
        current_scene = {}

        for line in lines:
            line = line.strip()
            if line.startswith('【镜头') and '】' in line:
                # 保存上一个场景
                if current_scene:
                    self.storyboard_scenes.append(current_scene)

                # 开始新场景
                current_scene = {
                    'title': line,
                    'scene': '',
                    'composition': '',
                    'content': '',
                    'duration': '',
                    'notes': ''
                }
            elif line.startswith('场景：'):
                current_scene['scene'] = line[3:]
            elif line.startswith('构图：'):
                current_scene['composition'] = line[3:]
            elif line.startswith('内容：'):
                current_scene['content'] = line[3:]
            elif line.startswith('时长：'):
                current_scene['duration'] = line[3:]
            elif line.startswith('备注：'):
                current_scene['notes'] = line[3:]

        # 添加最后一个场景
        if current_scene:
            self.storyboard_scenes.append(current_scene)

        # 更新场景列表
        for i, scene in enumerate(self.storyboard_scenes):
            item = QListWidgetItem(f"{i+1}. {scene['title']}")
            item.setData(Qt.UserRole, i)
            self.scene_list.addItem(item)

        self.statusBar().showMessage(f"解析完成，共{len(self.storyboard_scenes)}个场景")

    def select_scene(self, item):
        """选择场景"""
        scene_index = item.data(Qt.UserRole)
        if scene_index < len(self.storyboard_scenes):
            scene = self.storyboard_scenes[scene_index]

            self.current_scene_label.setText(scene['title'])

            # 生成图片提示词
            prompt = self.generate_image_prompt(scene)
            self.scene_prompt.setPlainText(prompt)

    def generate_image_prompt(self, scene):
        """生成图片提示词"""
        prompt_parts = []

        # 场景描述
        if scene['scene']:
            prompt_parts.append(f"Scene: {scene['scene']}")

        # 构图
        if scene['composition']:
            prompt_parts.append(f"Composition: {scene['composition']}")

        # 内容描述
        if scene['content']:
            prompt_parts.append(f"Content: {scene['content']}")

        # 备注
        if scene['notes']:
            prompt_parts.append(f"Notes: {scene['notes']}")

        return ", ".join(prompt_parts)

    def optimize_prompt(self):
        """优化提示词"""
        current_prompt = self.scene_prompt.toPlainText()
        if not current_prompt.strip():
            QMessageBox.warning(self, "警告", "请先选择场景")
            return

        # 使用AI优化提示词
        optimization_prompt = f"""
请优化以下图片生成提示词，使其更适合AI图片生成：

原始提示词：
{current_prompt}

优化要求：
1. 使用英文关键词
2. 描述要具体生动
3. 包含画面构图信息
4. 添加艺术风格描述
5. 确保语法正确

请提供优化后的提示词：
"""

        try:
            optimized = self.ai_generator._call_ai_api(optimization_prompt, "prompt_optimization")
            self.scene_prompt.setPlainText(optimized)
            self.statusBar().showMessage("提示词优化完成")
        except Exception as e:
            QMessageBox.critical(self, "错误", f"优化失败: {e}")

    def generate_single_image(self):
        """生成单张图片"""
        prompt = self.scene_prompt.toPlainText().strip()
        if not prompt:
            QMessageBox.warning(self, "警告", "请先选择场景或输入提示词")
            return

        style = self.style_combo.currentText()

        # 开始生成
        self.start_image_generation(prompt, style)

    def generate_all_images(self):
        """生成全部图片"""
        if not self.storyboard_scenes:
            QMessageBox.warning(self, "警告", "请先解析分镜脚本")
            return

        # 简化实现：逐个生成
        QMessageBox.information(self, "提示", "批量生成功能开发中，请使用单张生成")

    def start_image_generation(self, prompt, style):
        """开始图片生成"""
        # 禁用按钮
        self.set_buttons_enabled(False)

        # 显示进度条
        self.progress_bar.setVisible(True)
        self.progress_bar.setRange(0, 0)

        # 更新状态
        self.statusBar().showMessage("正在生成图片...")

        # 创建工作线程
        self.worker = ImageGenerationWorker(self.ai_generator, prompt, style)
        self.worker.finished.connect(self.on_image_generated)
        self.worker.error.connect(self.on_generation_error)
        self.worker.start()

    def on_image_generated(self, image_url):
        """图片生成完成"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.set_buttons_enabled(True)

        if image_url.startswith("错误") or image_url.startswith("图片生成失败"):
            QMessageBox.critical(self, "生成失败", image_url)
            self.statusBar().showMessage("图片生成失败")
            return

        # 显示图片
        try:
            # 更新图片显示
            self.display_generated_image(image_url)

            # 更新图片信息
            current_prompt = self.scene_prompt.toPlainText()
            style = self.style_combo.currentText()
            self.image_info_label.setText(f"风格: {style} | 提示词: {current_prompt[:50]}...")

            # 添加到图片库
            item = QListWidgetItem(f"图片 {self.gallery_list.count() + 1} - {style}")
            item.setData(Qt.UserRole, {
                'url': image_url,
                'prompt': current_prompt,
                'style': style,
                'timestamp': self.get_current_timestamp()
            })
            self.gallery_list.addItem(item)

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

            self.statusBar().showMessage("图片生成完成")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"显示图片失败: {e}")

    def on_generation_error(self, error_msg):
        """生成错误"""
        # 隐藏进度条
        self.progress_bar.setVisible(False)

        # 启用按钮
        self.set_buttons_enabled(True)

        # 显示错误
        QMessageBox.critical(self, "生成失败", f"图片生成失败:\n{error_msg}")
        self.statusBar().showMessage("生成失败")

    def set_buttons_enabled(self, enabled):
        """设置按钮启用状态"""
        self.generate_single_btn.setEnabled(enabled)
        self.generate_all_btn.setEnabled(enabled)
        self.optimize_prompt_btn.setEnabled(enabled)

    def refresh_web(self):
        """刷新网页"""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                self.web_view.reload()
                logging.info("网页刷新成功")
            else:
                # WebEngine不可用，打开外部浏览器
                import webbrowser
                webbrowser.open("https://www.doubao.com/chat/")
                self.statusBar().showMessage("已在外部浏览器中打开豆包网站")
        except Exception as e:
            logging.error(f"刷新网页失败: {e}")
            # 备用方案：打开浏览器
            import webbrowser
            webbrowser.open("https://www.doubao.com/chat/")
            self.statusBar().showMessage("网页刷新失败，已在外部浏览器中打开")

    def go_home(self):
        """返回首页"""
        try:
            if hasattr(self, 'web_view') and self.web_view is not None:
                self.web_view.setUrl(QUrl("https://www.doubao.com/chat/"))
                logging.info("已返回豆包首页")
                self.statusBar().showMessage("已返回豆包首页")
            else:
                # WebEngine不可用，打开外部浏览器
                import webbrowser
                webbrowser.open("https://www.doubao.com/chat/")
                self.statusBar().showMessage("已在外部浏览器中打开豆包网站")
        except Exception as e:
            logging.error(f"返回首页失败: {e}")
            # 备用方案：打开浏览器
            import webbrowser
            webbrowser.open("https://www.doubao.com/chat/")
            self.statusBar().showMessage("返回首页失败，已在外部浏览器中打开")





    def view_gallery_image(self, item):
        """查看图片库中的图片"""
        image_data = item.data(Qt.UserRole)
        if image_data:
            if isinstance(image_data, dict):
                # 新格式的数据
                image_url = image_data.get('url', '')
                prompt = image_data.get('prompt', '')
                style = image_data.get('style', '')
                timestamp = image_data.get('timestamp', '')

                # 显示图片
                self.display_generated_image(image_url)

                # 更新信息
                self.image_info_label.setText(
                    f"风格: {style} | 时间: {timestamp} | 提示词: {prompt[:30]}..."
                )

                # 更新提示词显示
                self.scene_prompt.setPlainText(prompt)
            else:
                # 兼容旧格式
                self.display_generated_image(image_data)
                self.image_info_label.setText("图片信息: 旧格式数据")

            # 切换到结果标签页
            self.tab_widget.setCurrentIndex(1)

    def clear_gallery(self):
        """清空图片库"""
        reply = QMessageBox.question(
            self, "确认清空",
            "确定要清空图片库吗？",
            QMessageBox.Yes | QMessageBox.No,
            QMessageBox.No
        )
        if reply == QMessageBox.Yes:
            self.gallery_list.clear()
            self.statusBar().showMessage("图片库已清空")

    def export_images(self):
        """导出图片"""
        if self.gallery_list.count() == 0:
            QMessageBox.warning(self, "警告", "图片库为空")
            return

        # 选择导出目录
        export_dir = QFileDialog.getExistingDirectory(self, "选择导出目录")
        if export_dir:
            # 简化实现
            QMessageBox.information(self, "提示", f"将导出到: {export_dir}")

    def export_all_images(self):
        """导出全部图片"""
        self.export_images()

    def batch_generate(self):
        """批量生成"""
        QMessageBox.information(self, "提示", "批量生成功能开发中")

    def display_generated_image(self, image_url):
        """显示生成的图片"""
        try:
            if image_url.startswith('http'):
                # 如果是URL，显示链接信息
                self.image_display.setText(f"""
                <div style='text-align: center; padding: 20px;'>
                    <h3>图片生成成功！</h3>
                    <p><b>图片链接：</b></p>
                    <p style='word-break: break-all; color: blue;'>{image_url}</p>
                    <br>
                    <p><b>使用说明：</b></p>
                    <p>1. 复制上方链接到浏览器打开</p>
                    <p>2. 右键保存图片到本地</p>
                    <p>3. 或使用下方"保存图片"按钮</p>
                </div>
                """)
                self.current_image_url = image_url
            else:
                # 如果是本地文件路径
                if os.path.exists(image_url):
                    pixmap = QPixmap(image_url)
                    if not pixmap.isNull():
                        # 缩放图片以适应显示区域
                        scaled_pixmap = pixmap.scaled(
                            600, 400,
                            Qt.KeepAspectRatio,
                            Qt.SmoothTransformation
                        )
                        self.image_display.setPixmap(scaled_pixmap)
                        self.current_image_url = image_url
                    else:
                        self.image_display.setText("图片加载失败")
                else:
                    self.image_display.setText(f"图片文件不存在: {image_url}")
        except Exception as e:
            logging.error(f"显示图片失败: {e}")
            self.image_display.setText(f"图片显示错误: {e}")

    def get_current_timestamp(self):
        """获取当前时间戳"""
        from datetime import datetime
        return datetime.now().strftime("%Y-%m-%d %H:%M:%S")

    def save_current_image(self):
        """保存当前图片"""
        if not hasattr(self, 'current_image_url') or not self.current_image_url:
            QMessageBox.warning(self, "警告", "没有图片可保存")
            return

        try:
            if self.current_image_url.startswith('http'):
                # 网络图片，提供下载链接
                reply = QMessageBox.question(
                    self, "保存图片",
                    f"图片链接：{self.current_image_url}\n\n"
                    "请选择保存方式：\n"
                    "是(Yes) - 复制链接到剪贴板\n"
                    "否(No) - 在浏览器中打开",
                    QMessageBox.Yes | QMessageBox.No | QMessageBox.Cancel
                )

                if reply == QMessageBox.Yes:
                    # 复制链接到剪贴板
                    from PySide6.QtWidgets import QApplication
                    clipboard = QApplication.clipboard()
                    clipboard.setText(self.current_image_url)
                    QMessageBox.information(self, "成功", "图片链接已复制到剪贴板")
                elif reply == QMessageBox.No:
                    # 在浏览器中打开
                    import webbrowser
                    webbrowser.open(self.current_image_url)
            else:
                # 本地图片，复制到指定位置
                save_path, _ = QFileDialog.getSaveFileName(
                    self, "保存图片", "",
                    "图片文件 (*.png *.jpg *.jpeg *.bmp);;所有文件 (*.*)"
                )
                if save_path:
                    import shutil
                    shutil.copy2(self.current_image_url, save_path)
                    QMessageBox.information(self, "成功", f"图片已保存到: {save_path}")

        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存图片失败: {e}")

    def copy_prompt(self):
        """复制提示词"""
        prompt = self.scene_prompt.toPlainText()
        if prompt:
            from PySide6.QtWidgets import QApplication
            clipboard = QApplication.clipboard()
            clipboard.setText(prompt)
            self.statusBar().showMessage("提示词已复制到剪贴板")
        else:
            QMessageBox.warning(self, "警告", "没有提示词可复制")

    def test_generate_image(self):
        """测试图片生成功能"""
        # 模拟图片生成成功
        test_url = "https://example.com/test-image.jpg"
        test_prompt = "测试提示词: 一个美丽的风景画，包含山川河流，艺术风格"

        # 设置测试提示词
        self.scene_prompt.setPlainText(test_prompt)

        # 模拟生成完成
        self.display_generated_image(test_url)

        # 更新图片信息
        style = self.style_combo.currentText()
        self.image_info_label.setText(f"风格: {style} | 测试图片 | 提示词: {test_prompt[:30]}...")

        # 添加到图片库
        item = QListWidgetItem(f"测试图片 {self.gallery_list.count() + 1} - {style}")
        item.setData(Qt.UserRole, {
            'url': test_url,
            'prompt': test_prompt,
            'style': style,
            'timestamp': self.get_current_timestamp()
        })
        self.gallery_list.addItem(item)

        # 切换到结果标签页
        self.tab_widget.setCurrentIndex(1)

        self.statusBar().showMessage("测试图片生成完成")

        QMessageBox.information(
            self, "测试完成",
            "测试图片已生成！\n\n"
            "功能说明：\n"
            "1. 生成结果标签页显示图片信息\n"
            "2. 可以保存图片链接\n"
            "3. 可以复制提示词\n"
            "4. 图片库记录所有生成的图片"
        )

    def _add_webengine_fallback_info(self, layout):
        """添加WebEngine备用信息"""
        web_info = QTextEdit()
        web_info.setReadOnly(True)
        web_info.setHtml("""
        <div style='padding: 20px; text-align: center;'>
            <h3>🌐 豆包生图使用说明</h3>
            <p style='color: orange;'><b>WebEngine组件不可用，请使用外部浏览器</b></p>

            <h4>📋 使用步骤：</h4>
            <ol style='text-align: left; max-width: 500px; margin: 0 auto;'>
                <li>点击下方"刷新"或"主页"按钮打开豆包网站</li>
                <li>在网页中输入生成的图片提示词</li>
                <li>添加"请生成图片"或"画一张图"等指令</li>
                <li>等待豆包生成图片</li>
                <li>右键保存生成的图片到本地</li>
            </ol>

            <h4>💡 提示词示例：</h4>
            <p style='background: #f0f0f0; padding: 10px; border-radius: 5px;'>
                请根据以下描述生成一张图片：一个古装美女推门而入，中景构图，古代房间背景，写实风格
            </p>

            <h4>🔧 解决方案：</h4>
            <p>如需内嵌网页功能，请运行 <code>python diagnose_webengine.py</code> 进行诊断</p>
        </div>
        """)
        layout.addWidget(web_info)
