"""
测试主角设定和项目数据自动保存功能
"""

import sys
import os
import time
import json

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_protagonist_settings():
    """测试主角设定功能"""
    print("=== 测试主角设定功能 ===")
    
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    from ui.novel_window import NovelWindow
    window = NovelWindow()
    
    # 1. 检查主角设定界面
    print("1. 检查主角设定界面...")
    
    # 检查主角设定控件是否存在
    controls = [
        ('protagonist_name', '主角姓名输入框'),
        ('protagonist_gender', '主角性别下拉框'),
        ('protagonist_age', '主角年龄输入框'),
        ('protagonist_occupation', '主角职业输入框')
    ]
    
    for control_name, description in controls:
        if hasattr(window, control_name):
            print(f"   ✅ {description} 存在")
        else:
            print(f"   ❌ {description} 不存在")
    
    # 2. 测试主角信息设置
    print("2. 测试主角信息设置...")
    
    # 设置主角信息
    test_protagonist = {
        "name": "李明",
        "gender": "男",
        "age": "25岁",
        "occupation": "程序员"
    }
    
    window.protagonist_name.setText(test_protagonist["name"])
    window.protagonist_age.setText(test_protagonist["age"])
    window.protagonist_occupation.setText(test_protagonist["occupation"])
    
    # 设置性别
    gender_index = window.protagonist_gender.findText(test_protagonist["gender"])
    if gender_index >= 0:
        window.protagonist_gender.setCurrentIndex(gender_index)
    
    print(f"   设置主角姓名: {window.protagonist_name.text()}")
    print(f"   设置主角性别: {window.protagonist_gender.currentText()}")
    print(f"   设置主角年龄: {window.protagonist_age.text()}")
    print(f"   设置主角职业: {window.protagonist_occupation.text()}")
    
    # 3. 测试角色生成功能
    print("3. 测试角色生成功能...")
    
    # 设置故事大纲（角色生成需要）
    window.outline_output.setPlainText("这是一个关于程序员李明的故事，他在一家科技公司工作...")
    
    # 检查生成角色设定按钮
    if hasattr(window, 'generate_character_btn'):
        print("   ✅ 生成角色设定按钮存在")
        # 这里可以测试按钮点击，但需要AI服务
        print("   角色生成功能已集成主角信息")
    else:
        print("   ❌ 生成角色设定按钮不存在")
    
    return True

def test_auto_save_functionality():
    """测试项目数据自动保存功能"""
    print("\n=== 测试项目数据自动保存功能 ===")
    
    from PySide6.QtWidgets import QApplication
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    from ui.novel_window import NovelWindow
    window = NovelWindow()
    
    # 1. 检查自动保存定时器
    print("1. 检查自动保存定时器...")
    
    if hasattr(window, 'project_save_timer'):
        print("   ✅ 项目数据自动保存定时器已创建")
        print(f"   自动保存延迟: {window.project_save_delay}ms")
    else:
        print("   ❌ 项目数据自动保存定时器未创建")
    
    # 2. 测试项目数据设置
    print("2. 测试项目数据设置...")
    
    # 设置测试数据
    test_data = {
        "title": "测试小说",
        "author": "测试作者",
        "theme": "科幻冒险",
        "genre": "科幻",
        "outline": "这是一个测试故事大纲...",
        "chapters": "第一章：开始\n第二章：发展",
        "character": "主角：李明，程序员",
        "world": "现代都市背景"
    }
    
    window.novel_title.setText(test_data["title"])
    window.author_name.setText(test_data["author"])
    window.theme_input.setText(test_data["theme"])
    window.outline_output.setPlainText(test_data["outline"])
    window.chapters_output.setPlainText(test_data["chapters"])
    window.character_output.setPlainText(test_data["character"])
    window.world_output.setPlainText(test_data["world"])
    
    # 设置类型
    genre_index = window.genre_combo.findText(test_data["genre"])
    if genre_index >= 0:
        window.genre_combo.setCurrentIndex(genre_index)
    
    print("   测试数据已设置")
    
    # 3. 手动触发自动保存
    print("3. 测试自动保存功能...")
    
    window.auto_save_project_data()
    
    # 检查自动保存文件
    auto_save_file = os.path.join("projects", "auto_save.json")
    if os.path.exists(auto_save_file):
        print("   ✅ 自动保存文件已创建")
        
        # 读取并验证保存的数据
        try:
            with open(auto_save_file, 'r', encoding='utf-8') as f:
                saved_data = json.load(f)
            
            print("   ✅ 自动保存文件格式正确")
            print(f"   保存的标题: {saved_data.get('title', '')}")
            print(f"   保存的作者: {saved_data.get('author', '')}")
            print(f"   保存时间: {saved_data.get('last_saved', '')}")
            
            # 检查主角数据
            protagonist = saved_data.get('protagonist', {})
            if protagonist:
                print(f"   主角姓名: {protagonist.get('name', '')}")
                print(f"   主角职业: {protagonist.get('occupation', '')}")
            
        except Exception as e:
            print(f"   ❌ 读取自动保存文件失败: {e}")
    else:
        print("   ❌ 自动保存文件未创建")
    
    # 4. 测试数据恢复功能
    print("4. 测试数据恢复功能...")
    
    # 创建新窗口实例测试恢复
    window2 = NovelWindow()
    
    # 检查数据是否恢复
    if window2.novel_title.text() == test_data["title"]:
        print("   ✅ 小说标题恢复成功")
    else:
        print(f"   ❌ 小说标题恢复失败: 期望'{test_data['title']}', 实际'{window2.novel_title.text()}'")
    
    if window2.author_name.text() == test_data["author"]:
        print("   ✅ 作者名字恢复成功")
    else:
        print(f"   ❌ 作者名字恢复失败")
    
    return True

def test_data_persistence():
    """测试数据持久化"""
    print("\n=== 测试数据持久化 ===")
    
    # 检查项目目录
    project_dir = "projects"
    if os.path.exists(project_dir):
        print(f"   ✅ 项目目录存在: {project_dir}")
        
        files = os.listdir(project_dir)
        print(f"   项目文件数量: {len(files)}")
        for file in files:
            print(f"     - {file}")
    else:
        print(f"   ❌ 项目目录不存在: {project_dir}")
    
    # 检查章节目录
    chapters_dir = "chapters"
    if os.path.exists(chapters_dir):
        print(f"   ✅ 章节目录存在: {chapters_dir}")
        
        files = os.listdir(chapters_dir)
        print(f"   章节文件数量: {len(files)}")
        for file in files[:5]:  # 只显示前5个
            print(f"     - {file}")
        if len(files) > 5:
            print(f"     ... 还有 {len(files) - 5} 个文件")
    else:
        print(f"   ❌ 章节目录不存在: {chapters_dir}")

def main():
    """主测试函数"""
    print("🔍 主角设定和项目数据自动保存功能测试")
    print("=" * 60)
    
    try:
        # 测试主角设定功能
        test_protagonist_settings()
        
        # 测试自动保存功能
        test_auto_save_functionality()
        
        # 测试数据持久化
        test_data_persistence()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        
        print("\n📋 功能总结:")
        print("1. 🎭 主角设定功能 - 自定义主角姓名、性别、年龄、职业")
        print("2. 🤖 AI集成功能 - 角色生成和章节续写使用主角信息")
        print("3. 💾 项目数据自动保存 - 所有界面数据实时自动保存")
        print("4. 🔄 数据恢复功能 - 程序重启后自动恢复上次数据")
        print("5. 📁 文件管理功能 - 项目和章节数据分别管理")
        
        print("\n🎯 使用说明:")
        print("- 在角色设定标签页设置主角信息")
        print("- 所有输入的数据会在3秒后自动保存")
        print("- 程序重启后会自动恢复上次的数据")
        print("- 生成角色设定和续写章节时会使用主角信息")
        print("- 项目数据保存在projects/auto_save.json")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
