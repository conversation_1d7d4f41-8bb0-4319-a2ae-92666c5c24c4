@echo off
chcp 65001 >nul
echo 启动小说AI文案生成器（安全模式）...
echo.

REM 设置环境变量禁用GPU加速
set QT_OPENGL=software
set QTWEBENGINE_DISABLE_SANDBOX=1
set QTWEBENGINE_CHROMIUM_FLAGS=--disable-gpu --disable-software-rasterizer --disable-gpu-sandbox

REM 检查Python是否安装
python --version >nul 2>&1
if errorlevel 1 (
    echo 错误: 未找到Python，请先安装Python 3.12+
    pause
    exit /b 1
)

REM 检查依赖是否安装
echo 检查依赖...
pip show PySide6 >nul 2>&1
if errorlevel 1 (
    echo 正在安装依赖...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo 错误: 依赖安装失败
        pause
        exit /b 1
    )
)

REM 启动程序
echo 启动程序（安全模式）...
python main.py

if errorlevel 1 (
    echo.
    echo 程序异常退出，请检查日志文件 logs/app.log
    pause
)
