"""
WebEngine问题诊断脚本
"""

import sys
import os
from pathlib import Path

def check_python_version():
    """检查Python版本"""
    print(f"Python版本: {sys.version}")
    if sys.version_info < (3, 8):
        print("❌ Python版本过低，建议使用Python 3.8+")
        return False
    else:
        print("✅ Python版本符合要求")
        return True

def check_pyside6():
    """检查PySide6安装"""
    try:
        import PySide6
        print(f"✅ PySide6版本: {PySide6.__version__}")
        return True
    except ImportError:
        print("❌ PySide6未安装")
        return False

def check_webengine():
    """检查WebEngine组件"""
    try:
        from PySide6.QtWebEngineWidgets import QWebEngineView
        print("✅ WebEngine组件可用")
        return True
    except ImportError as e:
        print(f"❌ WebEngine组件不可用: {e}")
        return False

def check_opengl():
    """检查OpenGL支持"""
    try:
        from PySide6.QtOpenGL import QOpenGLWidget
        print("✅ OpenGL组件可用")
        return True
    except ImportError as e:
        print(f"❌ OpenGL组件不可用: {e}")
        return False

def check_environment():
    """检查环境变量"""
    print("\n环境变量检查:")
    
    env_vars = [
        'QT_OPENGL',
        'QTWEBENGINE_DISABLE_SANDBOX',
        'QTWEBENGINE_CHROMIUM_FLAGS',
        'QT_LOGGING_RULES'
    ]
    
    for var in env_vars:
        value = os.environ.get(var)
        if value:
            print(f"✅ {var} = {value}")
        else:
            print(f"⚠️ {var} 未设置")

def test_webengine_creation():
    """测试WebEngine创建"""
    print("\n测试WebEngine创建...")
    
    try:
        # 设置环境变量
        os.environ['QT_OPENGL'] = 'software'
        os.environ['QTWEBENGINE_DISABLE_SANDBOX'] = '1'
        os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = '--disable-gpu --no-sandbox'
        
        from PySide6.QtWidgets import QApplication
        from PySide6.QtWebEngineWidgets import QWebEngineView
        
        app = QApplication([])
        
        # 创建WebEngine视图
        webview = QWebEngineView()
        print("✅ WebEngine视图创建成功")
        
        # 测试加载页面
        from PySide6.QtCore import QUrl
        webview.setUrl(QUrl("about:blank"))
        print("✅ 页面加载测试成功")
        
        return True
        
    except Exception as e:
        print(f"❌ WebEngine创建失败: {e}")
        return False

def get_system_info():
    """获取系统信息"""
    print("\n系统信息:")
    print(f"操作系统: {os.name}")
    print(f"平台: {sys.platform}")
    
    try:
        import platform
        print(f"系统版本: {platform.platform()}")
        print(f"处理器: {platform.processor()}")
    except:
        pass

def suggest_solutions():
    """建议解决方案"""
    print("\n🔧 WebEngine问题解决方案:")
    print("1. 更新显卡驱动到最新版本")
    print("2. 安装Visual C++ Redistributable")
    print("3. 使用管理员权限运行程序")
    print("4. 设置环境变量:")
    print("   set QT_OPENGL=software")
    print("   set QTWEBENGINE_DISABLE_SANDBOX=1")
    print("5. 如果问题持续，使用备用启动脚本 run.bat")
    print("6. 考虑使用虚拟环境重新安装依赖")

def main():
    """主函数"""
    print("🔍 WebEngine问题诊断工具")
    print("=" * 50)
    
    # 基础检查
    python_ok = check_python_version()
    pyside6_ok = check_pyside6()
    webengine_ok = check_webengine()
    opengl_ok = check_opengl()
    
    # 环境检查
    check_environment()
    
    # 系统信息
    get_system_info()
    
    # WebEngine测试
    if webengine_ok:
        webengine_test_ok = test_webengine_creation()
    else:
        webengine_test_ok = False
    
    # 总结
    print("\n📊 诊断结果:")
    print(f"Python: {'✅' if python_ok else '❌'}")
    print(f"PySide6: {'✅' if pyside6_ok else '❌'}")
    print(f"WebEngine: {'✅' if webengine_ok else '❌'}")
    print(f"OpenGL: {'✅' if opengl_ok else '❌'}")
    print(f"WebEngine测试: {'✅' if webengine_test_ok else '❌'}")
    
    if not webengine_test_ok:
        suggest_solutions()
    else:
        print("\n🎉 WebEngine工作正常！")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
