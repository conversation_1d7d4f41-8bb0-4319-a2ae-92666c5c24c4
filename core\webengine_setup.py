"""
WebEngine设置模块
解决GPU加速和上下文问题
"""

import os
import sys
import logging
from PySide6.QtCore import QCoreApplication

def setup_webengine_environment():
    """设置WebEngine环境变量"""
    
    # 禁用GPU硬件加速，使用软件渲染
    os.environ['QT_OPENGL'] = 'software'
    
    # WebEngine相关环境变量
    os.environ['QTWEBENGINE_DISABLE_SANDBOX'] = '1'
    os.environ['QTWEBENGINE_CHROMIUM_FLAGS'] = (
        '--disable-gpu '
        '--disable-gpu-sandbox '
        '--disable-software-rasterizer '
        '--disable-background-timer-throttling '
        '--disable-backgrounding-occluded-windows '
        '--disable-renderer-backgrounding '
        '--disable-features=TranslateUI '
        '--disable-ipc-flooding-protection '
        '--no-sandbox '
        '--single-process'
    )
    
    # 设置远程调试端口（可选）
    # os.environ['QTWEBENGINE_REMOTE_DEBUGGING'] = '9222'
    
    # 启用详细日志（调试用）
    os.environ['QT_LOGGING_RULES'] = 'qt.webenginecontext.debug=false'
    
    logging.info("WebEngine环境变量已设置")

def initialize_webengine():
    """初始化WebEngine"""
    try:
        # 设置环境变量
        setup_webengine_environment()
        
        # 导入WebEngine模块
        from PySide6.QtWebEngineWidgets import QWebEngineView
        from PySide6.QtWebEngineCore import QWebEngineSettings, QWebEngineProfile
        
        # 获取默认配置文件
        profile = QWebEngineProfile.defaultProfile()
        
        # 配置WebEngine设置
        settings = profile.settings()
        
        # 禁用可能导致问题的功能
        settings.setAttribute(QWebEngineSettings.WebAttribute.Accelerated2dCanvasEnabled, False)
        settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, False)
        settings.setAttribute(QWebEngineSettings.WebAttribute.PluginsEnabled, False)
        
        # 启用有用的功能
        settings.setAttribute(QWebEngineSettings.WebAttribute.JavascriptEnabled, True)
        settings.setAttribute(QWebEngineSettings.WebAttribute.LocalStorageEnabled, True)
        
        logging.info("WebEngine初始化成功")
        return True
        
    except Exception as e:
        logging.error(f"WebEngine初始化失败: {e}")
        return False

class SafeWebEngineView(QWebEngineView):
    """安全的WebEngineView包装类"""
    
    def __init__(self, parent=None):
        try:
            super().__init__(parent)
            self.setup_safe_settings()
        except Exception as e:
            logging.error(f"创建WebEngineView失败: {e}")
            raise
    
    def setup_safe_settings(self):
        """设置安全的WebEngine配置"""
        try:
            page = self.page()
            if page:
                settings = page.settings()
                
                # 禁用硬件加速相关功能
                settings.setAttribute(QWebEngineSettings.WebAttribute.Accelerated2dCanvasEnabled, False)
                settings.setAttribute(QWebEngineSettings.WebAttribute.WebGLEnabled, False)
                
                # 设置用户代理
                page.profile().setHttpUserAgent(
                    "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 "
                    "(KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36"
                )
                
        except Exception as e:
            logging.warning(f"设置WebEngine配置时出现警告: {e}")
    
    def setUrl(self, url):
        """安全的URL设置"""
        try:
            super().setUrl(url)
        except Exception as e:
            logging.error(f"设置URL失败: {e}")
    
    def load(self, url):
        """安全的页面加载"""
        try:
            super().load(url)
        except Exception as e:
            logging.error(f"加载页面失败: {e}")

def create_safe_webview(parent=None):
    """创建安全的WebView"""
    try:
        # 确保WebEngine已初始化
        if not initialize_webengine():
            return None
        
        # 创建WebView
        webview = SafeWebEngineView(parent)
        return webview
        
    except Exception as e:
        logging.error(f"创建WebView失败: {e}")
        return None
