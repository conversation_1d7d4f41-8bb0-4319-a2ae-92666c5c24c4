"""
AI文案生成器核心模块
支持多种AI提供商和文案类型生成
"""

import json
import requests
import logging
from typing import Dict, List, Optional
from pathlib import Path

class AIGenerator:
    """AI文案生成器"""
    
    def __init__(self, config_path: str = "config/settings.json"):
        """初始化AI生成器"""
        # 先初始化logger
        self.logger = logging.getLogger(__name__)
        # 再加载配置
        self.config = self._load_config(config_path)
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def generate_animation_script(self, novel_content: str, style: str = "搞笑沙雕") -> str:
        """生成沙雕动画文案"""
        prompt = f"""
请根据以下小说内容，生成一个{style}风格的短视频动画文案。

要求：
1. 文案要生动有趣，适合短视频传播
2. 突出故事的核心冲突和看点
3. 语言要通俗易懂，有网感
4. 控制在200字以内
5. 要有强烈的视觉画面感

小说内容：
{novel_content[:1000]}

请生成动画文案：
"""
        
        return self._call_ai_api(prompt, "animation_script")
    
    def generate_opening(self, novel_content: str, opening_type: str = "悬念开头") -> str:
        """生成爆款开头"""
        prompt = f"""
请根据以下小说内容，生成一个{opening_type}的爆款开头。

要求：
1. 开头要有强烈的吸引力，让人想继续看下去
2. 要符合{opening_type}的特点
3. 语言要精炼有力，有冲击力
4. 控制在100字以内
5. 要能体现故事的核心魅力

小说内容：
{novel_content[:800]}

请生成爆款开头：
"""

        return self._call_ai_api(prompt, "opening")

    def generate_storyboard(self, script_content: str, style: str = "动画分镜") -> str:
        """生成分镜脚本"""
        prompt = f"""
请根据以下动画文案，生成详细的{style}分镜脚本。

要求：
1. 将文案分解为多个镜头场景
2. 每个镜头包含：镜头号、场景描述、角色动作、画面构图、时长
3. 注重视觉效果和节奏感
4. 适合短视频制作
5. 镜头切换要流畅自然

文案内容：
{script_content}

请按以下格式生成分镜脚本：

【镜头1】
场景：[场景描述]
构图：[画面构图，如特写、中景、全景等]
内容：[具体画面内容和角色动作]
时长：[建议时长，如2-3秒]
备注：[特殊效果或注意事项]

【镜头2】
...

请生成分镜脚本：
"""

        return self._call_ai_api(prompt, "storyboard")
    
    def _call_ai_api(self, prompt: str, task_type: str) -> str:
        """调用AI API（带智能切换）"""
        try:
            provider = self.config.get("ai", {}).get("default_provider", "openai")
            provider_config = self.config.get("ai", {}).get("providers", {}).get(provider, {})

            if not provider_config.get("api_key"):
                # 如果当前提供商没有配置，尝试找一个有配置的
                return self._find_available_provider(prompt)

            # 尝试调用主要提供商
            try:
                if provider == "openai":
                    return self._call_openai_api(prompt, provider_config)
                elif provider == "qianwen":
                    return self._call_qianwen_api(prompt, provider_config)
                elif provider == "deepseek":
                    return self._call_deepseek_api(prompt, provider_config)
                elif provider == "gemini":
                    return self._call_gemini_api(prompt, provider_config)
                elif provider == "doubao":
                    return self._call_doubao_api(prompt, provider_config)
                else:
                    return self._find_available_provider(prompt)

            except Exception as provider_error:
                self.logger.warning(f"主要提供商 {provider} 失败: {provider_error}")
                # 主要提供商失败，尝试备用方案
                return self._try_fallback_provider(prompt)

        except Exception as e:
            self.logger.error(f"AI API调用失败: {e}")
            return f"生成失败：{str(e)}"

    def _find_available_provider(self, prompt: str) -> str:
        """查找可用的AI提供商"""
        providers = ["deepseek", "gemini", "doubao", "openai", "qianwen"]

        for provider in providers:
            provider_config = self.config.get("ai", {}).get("providers", {}).get(provider, {})

            if provider_config.get("api_key"):
                self.logger.info(f"找到可用的提供商: {provider}")

                try:
                    if provider == "openai":
                        return self._call_openai_api(prompt, provider_config)
                    elif provider == "qianwen":
                        return self._call_qianwen_api(prompt, provider_config)
                    elif provider == "deepseek":
                        return self._call_deepseek_api(prompt, provider_config)
                    elif provider == "gemini":
                        return self._call_gemini_api(prompt, provider_config)
                    elif provider == "doubao":
                        return self._call_doubao_api(prompt, provider_config)
                except Exception as e:
                    self.logger.warning(f"提供商 {provider} 调用失败: {e}")
                    continue

        return "错误：没有可用的AI提供商，请检查API配置"
    
    def _call_openai_api(self, prompt: str, config: Dict) -> str:
        """调用OpenAI API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config.get("model", "gpt-3.5-turbo"),
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
            "temperature": self.config.get("generation", {}).get("temperature", 0.8)
        }
        
        response = requests.post(
            f"{config.get('base_url', 'https://api.openai.com/v1')}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        else:
            raise Exception(f"API调用失败: {response.status_code} - {response.text}")
    
    def _call_qianwen_api(self, prompt: str, config: Dict) -> str:
        """调用通义千问API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config.get("model", "qwen-turbo"),
            "input": {
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            },
            "parameters": {
                "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
                "temperature": self.config.get("generation", {}).get("temperature", 0.8)
            }
        }
        
        response = requests.post(
            f"{config.get('base_url')}/services/aigc/text-generation/generation",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result["output"]["text"].strip()
        else:
            raise Exception(f"API调用失败: {response.status_code} - {response.text}")

    def _call_deepseek_api(self, prompt: str, config: Dict) -> str:
        """调用DeepSeek API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": config.get("model", "deepseek-chat"),
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
            "temperature": self.config.get("generation", {}).get("temperature", 0.8)
        }

        response = requests.post(
            f"{config.get('base_url', 'https://api.deepseek.com/v1')}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        else:
            raise Exception(f"DeepSeek API调用失败: {response.status_code} - {response.text}")

    def _call_gemini_api(self, prompt: str, config: Dict) -> str:
        """调用Gemini API（带重试机制）"""
        import time

        model = config.get("model", "gemini-1.5-flash")
        api_key = config['api_key']
        base_url = config.get('base_url', 'https://generativelanguage.googleapis.com/v1beta')

        headers = {
            "Content-Type": "application/json"
        }

        data = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt}
                    ]
                }
            ],
            "generationConfig": {
                "maxOutputTokens": self.config.get("generation", {}).get("max_tokens", 2000),
                "temperature": self.config.get("generation", {}).get("temperature", 0.8)
            }
        }

        # 重试机制：最多重试3次
        max_retries = 3
        retry_delays = [2, 5, 10]  # 重试延迟时间（秒）

        for attempt in range(max_retries):
            try:
                response = requests.post(
                    f"{base_url}/models/{model}:generateContent?key={api_key}",
                    headers=headers,
                    json=data,
                    timeout=60  # 增加超时时间
                )

                if response.status_code == 200:
                    result = response.json()
                    if "candidates" in result and len(result["candidates"]) > 0:
                        content = result["candidates"][0]["content"]["parts"][0]["text"]
                        return content.strip()
                    else:
                        raise Exception("Gemini API返回格式异常")

                elif response.status_code == 503:
                    # 503错误：服务过载，需要重试
                    if attempt < max_retries - 1:
                        delay = retry_delays[attempt]
                        self.logger.warning(f"Gemini API服务过载，{delay}秒后重试（第{attempt + 1}次）...")
                        time.sleep(delay)
                        continue
                    else:
                        # 最后一次重试失败，尝试备用方案
                        return self._try_fallback_provider(prompt)

                elif response.status_code == 429:
                    # 429错误：请求过于频繁
                    if attempt < max_retries - 1:
                        delay = retry_delays[attempt] * 2  # 频率限制时延迟更长
                        self.logger.warning(f"Gemini API请求频率过高，{delay}秒后重试（第{attempt + 1}次）...")
                        time.sleep(delay)
                        continue
                    else:
                        return self._try_fallback_provider(prompt)

                else:
                    # 其他错误
                    error_msg = f"Gemini API调用失败: {response.status_code} - {response.text}"
                    if attempt < max_retries - 1:
                        self.logger.warning(f"{error_msg}，正在重试...")
                        time.sleep(retry_delays[attempt])
                        continue
                    else:
                        raise Exception(error_msg)

            except requests.exceptions.Timeout:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Gemini API请求超时，正在重试（第{attempt + 1}次）...")
                    time.sleep(retry_delays[attempt])
                    continue
                else:
                    return self._try_fallback_provider(prompt)

            except requests.exceptions.RequestException as e:
                if attempt < max_retries - 1:
                    self.logger.warning(f"Gemini API网络错误: {e}，正在重试...")
                    time.sleep(retry_delays[attempt])
                    continue
                else:
                    return self._try_fallback_provider(prompt)

        # 如果所有重试都失败，返回错误信息
        return "Gemini API调用失败，请稍后重试或检查网络连接"

    def _try_fallback_provider(self, prompt: str) -> str:
        """尝试备用AI提供商"""
        try:
            # 获取备用提供商列表
            fallback_providers = ["deepseek", "doubao", "openai"]
            current_provider = self.config.get("ai", {}).get("default_provider", "gemini")

            # 移除当前失败的提供商
            if current_provider in fallback_providers:
                fallback_providers.remove(current_provider)

            for provider in fallback_providers:
                provider_config = self.config.get("ai", {}).get("providers", {}).get(provider, {})

                if provider_config.get("api_key"):
                    self.logger.info(f"尝试使用备用提供商: {provider}")

                    try:
                        if provider == "deepseek":
                            return self._call_deepseek_api(prompt, provider_config)
                        elif provider == "doubao":
                            return self._call_doubao_api(prompt, provider_config)
                        elif provider == "openai":
                            return self._call_openai_api(prompt, provider_config)
                    except Exception as e:
                        self.logger.warning(f"备用提供商 {provider} 也失败了: {e}")
                        continue

            # 所有备用方案都失败
            return "所有AI提供商都暂时不可用，请稍后重试。建议：\n1. 检查网络连接\n2. 稍后再试\n3. 切换到其他AI提供商"

        except Exception as e:
            self.logger.error(f"备用方案执行失败: {e}")
            return "AI服务暂时不可用，请稍后重试"

    def _call_doubao_api(self, prompt: str, config: Dict) -> str:
        """调用豆包API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": config.get("model", "doubao-pro-4k"),
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
            "temperature": self.config.get("generation", {}).get("temperature", 0.8)
        }

        response = requests.post(
            f"{config.get('base_url', 'https://ark.cn-beijing.volces.com/api/v3')}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        else:
            raise Exception(f"豆包API调用失败: {response.status_code} - {response.text}")

    def generate_image_with_doubao(self, prompt: str, style: str = "realistic") -> str:
        """使用豆包生成图片"""
        provider_config = self.config.get("ai", {}).get("providers", {}).get("doubao", {})

        if not provider_config.get("api_key"):
            return "错误：请先配置豆包API密钥"

        headers = {
            "Authorization": f"Bearer {provider_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "doubao-seededit",
            "prompt": f"{prompt}, {style} style",
            "n": 1,
            "size": "1024x1024",
            "quality": "standard"
        }

        try:
            response = requests.post(
                f"{provider_config.get('base_url')}/images/generations",
                headers=headers,
                json=data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                return result["data"][0]["url"]
            else:
                raise Exception(f"图片生成失败: {response.status_code} - {response.text}")

        except Exception as e:
            self.logger.error(f"豆包图片生成失败: {e}")
            return f"图片生成失败：{str(e)}"

    def get_available_styles(self) -> List[str]:
        """获取可用的动画风格"""
        return self.config.get("generation", {}).get("animation_styles", [])
    
    def get_available_opening_types(self) -> List[str]:
        """获取可用的开头类型"""
        return self.config.get("generation", {}).get("opening_types", [])
