"""
AI文案生成器核心模块
支持多种AI提供商和文案类型生成
"""

import json
import requests
import logging
from typing import Dict, List, Optional
from pathlib import Path

class AIGenerator:
    """AI文案生成器"""
    
    def __init__(self, config_path: str = "config/settings.json"):
        """初始化AI生成器"""
        self.config = self._load_config(config_path)
        self.logger = logging.getLogger(__name__)
        
    def _load_config(self, config_path: str) -> Dict:
        """加载配置文件"""
        try:
            with open(config_path, 'r', encoding='utf-8') as f:
                return json.load(f)
        except Exception as e:
            self.logger.error(f"加载配置文件失败: {e}")
            return {}
    
    def generate_animation_script(self, novel_content: str, style: str = "搞笑沙雕") -> str:
        """生成沙雕动画文案"""
        prompt = f"""
请根据以下小说内容，生成一个{style}风格的短视频动画文案。

要求：
1. 文案要生动有趣，适合短视频传播
2. 突出故事的核心冲突和看点
3. 语言要通俗易懂，有网感
4. 控制在200字以内
5. 要有强烈的视觉画面感

小说内容：
{novel_content[:1000]}

请生成动画文案：
"""
        
        return self._call_ai_api(prompt, "animation_script")
    
    def generate_opening(self, novel_content: str, opening_type: str = "悬念开头") -> str:
        """生成爆款开头"""
        prompt = f"""
请根据以下小说内容，生成一个{opening_type}的爆款开头。

要求：
1. 开头要有强烈的吸引力，让人想继续看下去
2. 要符合{opening_type}的特点
3. 语言要精炼有力，有冲击力
4. 控制在100字以内
5. 要能体现故事的核心魅力

小说内容：
{novel_content[:800]}

请生成爆款开头：
"""

        return self._call_ai_api(prompt, "opening")

    def generate_storyboard(self, script_content: str, style: str = "动画分镜") -> str:
        """生成分镜脚本"""
        prompt = f"""
请根据以下动画文案，生成详细的{style}分镜脚本。

要求：
1. 将文案分解为多个镜头场景
2. 每个镜头包含：镜头号、场景描述、角色动作、画面构图、时长
3. 注重视觉效果和节奏感
4. 适合短视频制作
5. 镜头切换要流畅自然

文案内容：
{script_content}

请按以下格式生成分镜脚本：

【镜头1】
场景：[场景描述]
构图：[画面构图，如特写、中景、全景等]
内容：[具体画面内容和角色动作]
时长：[建议时长，如2-3秒]
备注：[特殊效果或注意事项]

【镜头2】
...

请生成分镜脚本：
"""

        return self._call_ai_api(prompt, "storyboard")
    
    def _call_ai_api(self, prompt: str, task_type: str) -> str:
        """调用AI API"""
        try:
            provider = self.config.get("ai", {}).get("default_provider", "openai")
            provider_config = self.config.get("ai", {}).get("providers", {}).get(provider, {})
            
            if not provider_config.get("api_key"):
                return "错误：请先配置AI API密钥"
            
            if provider == "openai":
                return self._call_openai_api(prompt, provider_config)
            elif provider == "qianwen":
                return self._call_qianwen_api(prompt, provider_config)
            elif provider == "deepseek":
                return self._call_deepseek_api(prompt, provider_config)
            elif provider == "gemini":
                return self._call_gemini_api(prompt, provider_config)
            elif provider == "doubao":
                return self._call_doubao_api(prompt, provider_config)
            else:
                return "错误：不支持的AI提供商"
                
        except Exception as e:
            self.logger.error(f"AI API调用失败: {e}")
            return f"生成失败：{str(e)}"
    
    def _call_openai_api(self, prompt: str, config: Dict) -> str:
        """调用OpenAI API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config.get("model", "gpt-3.5-turbo"),
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
            "temperature": self.config.get("generation", {}).get("temperature", 0.8)
        }
        
        response = requests.post(
            f"{config.get('base_url', 'https://api.openai.com/v1')}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        else:
            raise Exception(f"API调用失败: {response.status_code} - {response.text}")
    
    def _call_qianwen_api(self, prompt: str, config: Dict) -> str:
        """调用通义千问API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }
        
        data = {
            "model": config.get("model", "qwen-turbo"),
            "input": {
                "messages": [
                    {"role": "user", "content": prompt}
                ]
            },
            "parameters": {
                "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
                "temperature": self.config.get("generation", {}).get("temperature", 0.8)
            }
        }
        
        response = requests.post(
            f"{config.get('base_url')}/services/aigc/text-generation/generation",
            headers=headers,
            json=data,
            timeout=30
        )
        
        if response.status_code == 200:
            result = response.json()
            return result["output"]["text"].strip()
        else:
            raise Exception(f"API调用失败: {response.status_code} - {response.text}")

    def _call_deepseek_api(self, prompt: str, config: Dict) -> str:
        """调用DeepSeek API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": config.get("model", "deepseek-chat"),
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
            "temperature": self.config.get("generation", {}).get("temperature", 0.8)
        }

        response = requests.post(
            f"{config.get('base_url', 'https://api.deepseek.com/v1')}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        else:
            raise Exception(f"DeepSeek API调用失败: {response.status_code} - {response.text}")

    def _call_gemini_api(self, prompt: str, config: Dict) -> str:
        """调用Gemini API"""
        model = config.get("model", "gemini-1.5-flash")
        api_key = config['api_key']
        base_url = config.get('base_url', 'https://generativelanguage.googleapis.com/v1beta')

        headers = {
            "Content-Type": "application/json"
        }

        data = {
            "contents": [
                {
                    "parts": [
                        {"text": prompt}
                    ]
                }
            ],
            "generationConfig": {
                "maxOutputTokens": self.config.get("generation", {}).get("max_tokens", 2000),
                "temperature": self.config.get("generation", {}).get("temperature", 0.8)
            }
        }

        response = requests.post(
            f"{base_url}/models/{model}:generateContent?key={api_key}",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            if "candidates" in result and len(result["candidates"]) > 0:
                content = result["candidates"][0]["content"]["parts"][0]["text"]
                return content.strip()
            else:
                raise Exception("Gemini API返回格式异常")
        else:
            raise Exception(f"Gemini API调用失败: {response.status_code} - {response.text}")

    def _call_doubao_api(self, prompt: str, config: Dict) -> str:
        """调用豆包API"""
        headers = {
            "Authorization": f"Bearer {config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": config.get("model", "doubao-pro-4k"),
            "messages": [
                {"role": "user", "content": prompt}
            ],
            "max_tokens": self.config.get("generation", {}).get("max_tokens", 2000),
            "temperature": self.config.get("generation", {}).get("temperature", 0.8)
        }

        response = requests.post(
            f"{config.get('base_url', 'https://ark.cn-beijing.volces.com/api/v3')}/chat/completions",
            headers=headers,
            json=data,
            timeout=30
        )

        if response.status_code == 200:
            result = response.json()
            return result["choices"][0]["message"]["content"].strip()
        else:
            raise Exception(f"豆包API调用失败: {response.status_code} - {response.text}")

    def generate_image_with_doubao(self, prompt: str, style: str = "realistic") -> str:
        """使用豆包生成图片"""
        provider_config = self.config.get("ai", {}).get("providers", {}).get("doubao", {})

        if not provider_config.get("api_key"):
            return "错误：请先配置豆包API密钥"

        headers = {
            "Authorization": f"Bearer {provider_config['api_key']}",
            "Content-Type": "application/json"
        }

        data = {
            "model": "doubao-seededit",
            "prompt": f"{prompt}, {style} style",
            "n": 1,
            "size": "1024x1024",
            "quality": "standard"
        }

        try:
            response = requests.post(
                f"{provider_config.get('base_url')}/images/generations",
                headers=headers,
                json=data,
                timeout=60
            )

            if response.status_code == 200:
                result = response.json()
                return result["data"][0]["url"]
            else:
                raise Exception(f"图片生成失败: {response.status_code} - {response.text}")

        except Exception as e:
            self.logger.error(f"豆包图片生成失败: {e}")
            return f"图片生成失败：{str(e)}"

    def get_available_styles(self) -> List[str]:
        """获取可用的动画风格"""
        return self.config.get("generation", {}).get("animation_styles", [])
    
    def get_available_opening_types(self) -> List[str]:
        """获取可用的开头类型"""
        return self.config.get("generation", {}).get("opening_types", [])
