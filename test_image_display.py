"""
测试图片显示功能
"""

import sys
import os
from PySide6.QtWidgets import <PERSON>A<PERSON><PERSON>, QMainWindow, QPushButton, QVBoxLayout, QWidget, QLabel
from PySide6.QtCore import Qt
from PySide6.QtGui import QPixmap

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

class TestImageDisplay(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("图片显示测试")
        self.setGeometry(100, 100, 800, 600)
        
        # 创建中央部件
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        layout = QVBoxLayout(central_widget)
        
        # 创建测试按钮
        self.test_local_btn = QPushButton("测试本地图片显示")
        self.test_local_btn.clicked.connect(self.test_local_image)
        layout.addWidget(self.test_local_btn)
        
        self.test_url_btn = QPushButton("测试网络图片下载")
        self.test_url_btn.clicked.connect(self.test_network_image)
        layout.addWidget(self.test_url_btn)
        
        # 创建图片显示区域
        self.image_display = QLabel()
        self.image_display.setAlignment(Qt.AlignCenter)
        self.image_display.setText("图片将显示在这里")
        self.image_display.setStyleSheet("border: 1px solid gray; min-height: 400px;")
        layout.addWidget(self.image_display)
        
        # 状态标签
        self.status_label = QLabel("准备就绪")
        layout.addWidget(self.status_label)
    
    def test_local_image(self):
        """测试本地图片显示"""
        # 创建一个简单的测试图片
        try:
            from PySide6.QtGui import QPixmap, QPainter, QColor
            
            # 创建一个测试图片
            pixmap = QPixmap(400, 300)
            pixmap.fill(QColor(100, 150, 200))
            
            painter = QPainter(pixmap)
            painter.setPen(QColor(255, 255, 255))
            painter.drawText(pixmap.rect(), Qt.AlignCenter, "测试图片\nTest Image")
            painter.end()
            
            # 保存到临时文件
            import tempfile
            temp_dir = tempfile.gettempdir()
            test_file = os.path.join(temp_dir, "test_image.png")
            
            if pixmap.save(test_file, "PNG"):
                self.display_image(test_file)
                self.status_label.setText(f"本地图片测试成功: {test_file}")
            else:
                self.status_label.setText("本地图片保存失败")
                
        except Exception as e:
            self.status_label.setText(f"本地图片测试失败: {e}")
    
    def test_network_image(self):
        """测试网络图片下载"""
        try:
            # 使用一个公开的测试图片URL
            test_url = "https://httpbin.org/image/png"
            
            self.status_label.setText("正在下载网络图片...")
            
            # 创建下载线程
            from ui.image_generator_window import ImageDownloadWorker
            self.download_worker = ImageDownloadWorker(test_url)
            self.download_worker.download_finished.connect(self.on_download_success)
            self.download_worker.download_error.connect(self.on_download_error)
            self.download_worker.start()
            
        except Exception as e:
            self.status_label.setText(f"网络图片测试失败: {e}")
    
    def on_download_success(self, local_path):
        """下载成功"""
        self.display_image(local_path)
        self.status_label.setText(f"网络图片下载成功: {local_path}")
    
    def on_download_error(self, error_msg, url):
        """下载失败"""
        self.status_label.setText(f"网络图片下载失败: {error_msg}")
    
    def display_image(self, image_path):
        """显示图片"""
        try:
            if os.path.exists(image_path):
                pixmap = QPixmap(image_path)
                if not pixmap.isNull():
                    # 缩放图片以适应显示区域
                    scaled_pixmap = pixmap.scaled(
                        600, 400,
                        Qt.KeepAspectRatio,
                        Qt.SmoothTransformation
                    )
                    self.image_display.setPixmap(scaled_pixmap)
                    print(f"✅ 图片显示成功: {image_path}")
                else:
                    self.image_display.setText("图片加载失败")
                    print(f"❌ 图片加载失败: {image_path}")
            else:
                self.image_display.setText("图片文件不存在")
                print(f"❌ 图片文件不存在: {image_path}")
        except Exception as e:
            self.image_display.setText(f"显示错误: {e}")
            print(f"❌ 显示图片失败: {e}")

def main():
    app = QApplication(sys.argv)
    
    window = TestImageDisplay()
    window.show()
    
    return app.exec()

if __name__ == "__main__":
    sys.exit(main())
