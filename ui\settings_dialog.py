"""
设置对话框
用于配置AI API密钥和其他设置
"""

import json
from pathlib import Path
from PySide6.QtWidgets import (
    QDialog, QVBoxLayout, QHBoxLayout, QFormLayout,
    QLineEdit, QPushButton, QComboBox, QSpinBox,
    QDoubleSpinBox, QGroupBox, QMessageBox, QTabWidget,
    QWidget, QLabel, QTextEdit
)
from PySide6.QtCore import Qt
from PySide6.QtGui import QFont

class SettingsDialog(QDialog):
    """设置对话框"""
    
    def __init__(self, parent=None):
        super().__init__(parent)
        self.config = {}
        self.init_ui()
        self.load_settings()
    
    def init_ui(self):
        """初始化界面"""
        self.setWindowTitle("设置")
        self.setModal(True)
        self.resize(500, 400)
        
        layout = QVBoxLayout(self)
        
        # 创建标签页
        tab_widget = QTabWidget()
        
        # AI设置标签页
        ai_tab = self.create_ai_settings_tab()
        tab_widget.addTab(ai_tab, "AI设置")
        
        # 生成设置标签页
        generation_tab = self.create_generation_settings_tab()
        tab_widget.addTab(generation_tab, "生成设置")
        
        # 应用设置标签页
        app_tab = self.create_app_settings_tab()
        tab_widget.addTab(app_tab, "应用设置")
        
        layout.addWidget(tab_widget)
        
        # 按钮
        button_layout = QHBoxLayout()
        self.test_btn = QPushButton("测试连接")
        self.save_btn = QPushButton("保存")
        self.cancel_btn = QPushButton("取消")
        
        self.test_btn.clicked.connect(self.test_connection)
        self.save_btn.clicked.connect(self.save_settings)
        self.cancel_btn.clicked.connect(self.reject)
        
        button_layout.addWidget(self.test_btn)
        button_layout.addStretch()
        button_layout.addWidget(self.save_btn)
        button_layout.addWidget(self.cancel_btn)
        
        layout.addLayout(button_layout)
    
    def create_ai_settings_tab(self):
        """创建AI设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # AI提供商设置
        provider_group = QGroupBox("AI提供商")
        provider_layout = QFormLayout(provider_group)
        
        self.provider_combo = QComboBox()
        self.provider_combo.addItems(["openai", "qianwen", "deepseek", "gemini", "doubao"])
        self.provider_combo.currentTextChanged.connect(self.on_provider_changed)
        provider_layout.addRow("默认提供商:", self.provider_combo)
        
        layout.addWidget(provider_group)
        
        # OpenAI设置
        self.openai_group = QGroupBox("OpenAI设置")
        openai_layout = QFormLayout(self.openai_group)
        
        self.openai_api_key = QLineEdit()
        self.openai_api_key.setEchoMode(QLineEdit.Password)
        self.openai_api_key.setPlaceholderText("输入OpenAI API密钥")
        openai_layout.addRow("API密钥:", self.openai_api_key)
        
        self.openai_model = QComboBox()
        self.openai_model.addItems([
            "gpt-4o", "gpt-4o-mini", "gpt-4-turbo", "gpt-4",
            "gpt-3.5-turbo", "o1-preview", "o1-mini"
        ])
        openai_layout.addRow("模型:", self.openai_model)
        
        self.openai_base_url = QLineEdit()
        self.openai_base_url.setPlaceholderText("https://api.openai.com/v1")
        openai_layout.addRow("API地址:", self.openai_base_url)
        
        layout.addWidget(self.openai_group)
        
        # 通义千问设置
        self.qianwen_group = QGroupBox("通义千问设置")
        qianwen_layout = QFormLayout(self.qianwen_group)
        
        self.qianwen_api_key = QLineEdit()
        self.qianwen_api_key.setEchoMode(QLineEdit.Password)
        self.qianwen_api_key.setPlaceholderText("输入通义千问API密钥")
        qianwen_layout.addRow("API密钥:", self.qianwen_api_key)
        
        self.qianwen_model = QComboBox()
        self.qianwen_model.addItems([
            "qwen-plus-latest", "qwen-max-latest", "qwen-turbo-latest",
            "qwen-plus", "qwen-max", "qwen-turbo", "qwen2.5-72b-instruct"
        ])
        qianwen_layout.addRow("模型:", self.qianwen_model)
        
        self.qianwen_base_url = QLineEdit()
        self.qianwen_base_url.setPlaceholderText("https://dashscope.aliyuncs.com/api/v1")
        qianwen_layout.addRow("API地址:", self.qianwen_base_url)
        
        layout.addWidget(self.qianwen_group)

        # DeepSeek设置
        self.deepseek_group = QGroupBox("DeepSeek设置")
        deepseek_layout = QFormLayout(self.deepseek_group)

        self.deepseek_api_key = QLineEdit()
        self.deepseek_api_key.setEchoMode(QLineEdit.Password)
        self.deepseek_api_key.setPlaceholderText("输入DeepSeek API密钥")
        deepseek_layout.addRow("API密钥:", self.deepseek_api_key)

        self.deepseek_model = QComboBox()
        self.deepseek_model.addItems([
            "deepseek-chat", "deepseek-coder", "deepseek-reasoner",
            "deepseek-v3", "deepseek-v2.5"
        ])
        deepseek_layout.addRow("模型:", self.deepseek_model)

        self.deepseek_base_url = QLineEdit()
        self.deepseek_base_url.setPlaceholderText("https://api.deepseek.com/v1")
        deepseek_layout.addRow("API地址:", self.deepseek_base_url)

        layout.addWidget(self.deepseek_group)

        # Gemini设置
        self.gemini_group = QGroupBox("Gemini设置")
        gemini_layout = QFormLayout(self.gemini_group)

        self.gemini_api_key = QLineEdit()
        self.gemini_api_key.setEchoMode(QLineEdit.Password)
        self.gemini_api_key.setPlaceholderText("输入Gemini API密钥")
        gemini_layout.addRow("API密钥:", self.gemini_api_key)

        self.gemini_model = QComboBox()
        self.gemini_model.addItems([
            "gemini-2.0-flash", "gemini-1.5-pro", "gemini-1.5-flash",
            "gemini-1.5-flash-8b", "gemini-1.0-pro"
        ])
        gemini_layout.addRow("模型:", self.gemini_model)

        self.gemini_base_url = QLineEdit()
        self.gemini_base_url.setPlaceholderText("https://generativelanguage.googleapis.com/v1beta")
        gemini_layout.addRow("API地址:", self.gemini_base_url)

        layout.addWidget(self.gemini_group)

        # 豆包设置
        self.doubao_group = QGroupBox("豆包设置")
        doubao_layout = QFormLayout(self.doubao_group)

        self.doubao_api_key = QLineEdit()
        self.doubao_api_key.setEchoMode(QLineEdit.Password)
        self.doubao_api_key.setPlaceholderText("输入豆包API密钥")
        doubao_layout.addRow("API密钥:", self.doubao_api_key)

        self.doubao_model = QComboBox()
        self.doubao_model.addItems([
            "doubao-pro-4k", "doubao-pro-32k", "doubao-lite-4k",
            "doubao-pro-128k", "doubao-character-4k"
        ])
        doubao_layout.addRow("模型:", self.doubao_model)

        self.doubao_base_url = QLineEdit()
        self.doubao_base_url.setPlaceholderText("https://ark.cn-beijing.volces.com/api/v3")
        doubao_layout.addRow("API地址:", self.doubao_base_url)

        layout.addWidget(self.doubao_group)

        layout.addStretch()

        return widget
    
    def create_generation_settings_tab(self):
        """创建生成设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 生成参数
        params_group = QGroupBox("生成参数")
        params_layout = QFormLayout(params_group)
        
        self.max_tokens = QSpinBox()
        self.max_tokens.setRange(100, 4000)
        self.max_tokens.setValue(2000)
        params_layout.addRow("最大令牌数:", self.max_tokens)
        
        self.temperature = QDoubleSpinBox()
        self.temperature.setRange(0.0, 2.0)
        self.temperature.setSingleStep(0.1)
        self.temperature.setValue(0.8)
        params_layout.addRow("创造性(Temperature):", self.temperature)
        
        layout.addWidget(params_group)
        
        # 风格设置
        styles_group = QGroupBox("动画风格")
        styles_layout = QVBoxLayout(styles_group)
        
        self.animation_styles = QTextEdit()
        self.animation_styles.setMaximumHeight(100)
        self.animation_styles.setPlaceholderText("每行一个风格，如：搞笑沙雕")
        styles_layout.addWidget(self.animation_styles)
        
        layout.addWidget(styles_group)
        
        # 开头类型设置
        openings_group = QGroupBox("开头类型")
        openings_layout = QVBoxLayout(openings_group)
        
        self.opening_types = QTextEdit()
        self.opening_types.setMaximumHeight(100)
        self.opening_types.setPlaceholderText("每行一个类型，如：悬念开头")
        openings_layout.addWidget(self.opening_types)
        
        layout.addWidget(openings_group)
        
        layout.addStretch()
        
        return widget
    
    def create_app_settings_tab(self):
        """创建应用设置标签页"""
        widget = QWidget()
        layout = QVBoxLayout(widget)
        
        # 窗口设置
        window_group = QGroupBox("窗口设置")
        window_layout = QFormLayout(window_group)
        
        self.window_width = QSpinBox()
        self.window_width.setRange(800, 2000)
        self.window_width.setValue(1200)
        window_layout.addRow("窗口宽度:", self.window_width)
        
        self.window_height = QSpinBox()
        self.window_height.setRange(600, 1500)
        self.window_height.setValue(800)
        window_layout.addRow("窗口高度:", self.window_height)
        
        layout.addWidget(window_group)
        
        layout.addStretch()
        
        return widget
    
    def on_provider_changed(self, provider):
        """提供商改变时的处理"""
        # 隐藏所有提供商组
        self.openai_group.setVisible(False)
        self.qianwen_group.setVisible(False)
        self.deepseek_group.setVisible(False)
        self.gemini_group.setVisible(False)
        self.doubao_group.setVisible(False)

        # 显示选中的提供商组
        if provider == "openai":
            self.openai_group.setVisible(True)
        elif provider == "qianwen":
            self.qianwen_group.setVisible(True)
        elif provider == "deepseek":
            self.deepseek_group.setVisible(True)
        elif provider == "gemini":
            self.gemini_group.setVisible(True)
        elif provider == "doubao":
            self.doubao_group.setVisible(True)
    
    def load_settings(self):
        """加载设置"""
        try:
            with open("config/settings.json", 'r', encoding='utf-8') as f:
                self.config = json.load(f)
            
            # 加载AI设置
            ai_config = self.config.get("ai", {})
            self.provider_combo.setCurrentText(ai_config.get("default_provider", "openai"))
            
            # OpenAI设置
            openai_config = ai_config.get("providers", {}).get("openai", {})
            self.openai_api_key.setText(openai_config.get("api_key", ""))
            self.openai_model.setCurrentText(openai_config.get("model", "gpt-3.5-turbo"))
            self.openai_base_url.setText(openai_config.get("base_url", "https://api.openai.com/v1"))
            
            # 通义千问设置
            qianwen_config = ai_config.get("providers", {}).get("qianwen", {})
            self.qianwen_api_key.setText(qianwen_config.get("api_key", ""))
            self.qianwen_model.setCurrentText(qianwen_config.get("model", "qwen-turbo"))
            self.qianwen_base_url.setText(qianwen_config.get("base_url", "https://dashscope.aliyuncs.com/api/v1"))

            # DeepSeek设置
            deepseek_config = ai_config.get("providers", {}).get("deepseek", {})
            self.deepseek_api_key.setText(deepseek_config.get("api_key", ""))
            self.deepseek_model.setCurrentText(deepseek_config.get("model", "deepseek-chat"))
            self.deepseek_base_url.setText(deepseek_config.get("base_url", "https://api.deepseek.com/v1"))

            # Gemini设置
            gemini_config = ai_config.get("providers", {}).get("gemini", {})
            self.gemini_api_key.setText(gemini_config.get("api_key", ""))
            self.gemini_model.setCurrentText(gemini_config.get("model", "gemini-1.5-flash"))
            self.gemini_base_url.setText(gemini_config.get("base_url", "https://generativelanguage.googleapis.com/v1beta"))
            
            # 生成设置
            gen_config = self.config.get("generation", {})
            self.max_tokens.setValue(gen_config.get("max_tokens", 2000))
            self.temperature.setValue(gen_config.get("temperature", 0.8))
            
            # 风格和类型
            styles = gen_config.get("animation_styles", [])
            self.animation_styles.setPlainText("\n".join(styles))
            
            types = gen_config.get("opening_types", [])
            self.opening_types.setPlainText("\n".join(types))
            
            # 应用设置
            app_config = self.config.get("app", {})
            window_size = app_config.get("window_size", [1200, 800])
            self.window_width.setValue(window_size[0])
            self.window_height.setValue(window_size[1])
            
            # 触发提供商改变事件
            self.on_provider_changed(self.provider_combo.currentText())
            
        except Exception as e:
            QMessageBox.warning(self, "警告", f"加载设置失败: {e}")
    
    def save_settings(self):
        """保存设置"""
        try:
            # 更新配置
            if "ai" not in self.config:
                self.config["ai"] = {"providers": {}}
            
            self.config["ai"]["default_provider"] = self.provider_combo.currentText()
            
            # OpenAI配置
            self.config["ai"]["providers"]["openai"] = {
                "api_key": self.openai_api_key.text(),
                "model": self.openai_model.currentText(),
                "base_url": self.openai_base_url.text() or "https://api.openai.com/v1"
            }
            
            # 通义千问配置
            self.config["ai"]["providers"]["qianwen"] = {
                "api_key": self.qianwen_api_key.text(),
                "model": self.qianwen_model.currentText(),
                "base_url": self.qianwen_base_url.text() or "https://dashscope.aliyuncs.com/api/v1"
            }

            # DeepSeek配置
            self.config["ai"]["providers"]["deepseek"] = {
                "api_key": self.deepseek_api_key.text(),
                "model": self.deepseek_model.currentText(),
                "base_url": self.deepseek_base_url.text() or "https://api.deepseek.com/v1"
            }

            # Gemini配置
            self.config["ai"]["providers"]["gemini"] = {
                "api_key": self.gemini_api_key.text(),
                "model": self.gemini_model.currentText(),
                "base_url": self.gemini_base_url.text() or "https://generativelanguage.googleapis.com/v1beta"
            }
            
            # 生成设置
            if "generation" not in self.config:
                self.config["generation"] = {}
            
            self.config["generation"]["max_tokens"] = self.max_tokens.value()
            self.config["generation"]["temperature"] = self.temperature.value()
            
            # 风格和类型
            styles_text = self.animation_styles.toPlainText().strip()
            self.config["generation"]["animation_styles"] = [s.strip() for s in styles_text.split('\n') if s.strip()]
            
            types_text = self.opening_types.toPlainText().strip()
            self.config["generation"]["opening_types"] = [t.strip() for t in types_text.split('\n') if t.strip()]
            
            # 应用设置
            if "app" not in self.config:
                self.config["app"] = {}
            
            self.config["app"]["window_size"] = [self.window_width.value(), self.window_height.value()]
            
            # 保存到文件
            with open("config/settings.json", 'w', encoding='utf-8') as f:
                json.dump(self.config, f, ensure_ascii=False, indent=4)
            
            QMessageBox.information(self, "成功", "设置已保存")
            self.accept()
            
        except Exception as e:
            QMessageBox.critical(self, "错误", f"保存设置失败: {e}")
    
    def test_connection(self):
        """测试AI连接"""
        provider = self.provider_combo.currentText()

        if provider == "openai":
            api_key = self.openai_api_key.text()
            if not api_key:
                QMessageBox.warning(self, "警告", "请先输入OpenAI API密钥")
                return
        elif provider == "qianwen":
            api_key = self.qianwen_api_key.text()
            if not api_key:
                QMessageBox.warning(self, "警告", "请先输入通义千问API密钥")
                return
        elif provider == "deepseek":
            api_key = self.deepseek_api_key.text()
            if not api_key:
                QMessageBox.warning(self, "警告", "请先输入DeepSeek API密钥")
                return
        elif provider == "gemini":
            api_key = self.gemini_api_key.text()
            if not api_key:
                QMessageBox.warning(self, "警告", "请先输入Gemini API密钥")
                return

        # 这里可以添加实际的连接测试逻辑
        QMessageBox.information(self, "提示", "连接测试功能待实现")
