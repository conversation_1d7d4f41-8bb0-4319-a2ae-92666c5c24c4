"""
小说AI文案生成器
主程序入口
"""

import sys
import logging
import os
from pathlib import Path
from PySide6.QtWidgets import QApplication, QMessageBox
from PySide6.QtCore import Qt
from PySide6.QtGui import QIcon

# 添加项目根目录到Python路径
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from ui.main_window import MainWindow

def setup_logging():
    """设置日志"""
    log_dir = Path("logs")
    log_dir.mkdir(exist_ok=True)
    
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler(log_dir / "app.log", encoding='utf-8'),
            logging.StreamHandler()
        ]
    )

def check_dependencies():
    """检查依赖"""
    try:
        import PySide6
        import requests
        return True, ""
    except ImportError as e:
        return False, f"缺少依赖: {e}"

def create_directories():
    """创建必要的目录"""
    directories = ["config", "logs", "output"]
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)

def main():
    """主函数"""
    # 设置日志
    setup_logging()
    logger = logging.getLogger(__name__)

    logger.info("启动小说AI文案生成器")

    # 检查依赖
    deps_ok, error_msg = check_dependencies()
    if not deps_ok:
        print(f"依赖检查失败: {error_msg}")
        print("请运行: pip install -r requirements.txt")
        return 1

    # 创建必要目录
    create_directories()

    # 初始化WebEngine环境
    try:
        from core.webengine_setup import setup_webengine_environment
        setup_webengine_environment()
        logger.info("WebEngine环境初始化成功")
    except Exception as e:
        logger.warning(f"WebEngine环境初始化失败: {e}")

    # 创建应用
    app = QApplication(sys.argv)
    app.setApplicationName("小说AI文案生成器")
    app.setApplicationVersion("3.0.0")
    app.setOrganizationName("AI文案工具")
    
    # 设置应用样式
    app.setStyle("Fusion")
    
    # 设置应用图标（如果有的话）
    # app.setWindowIcon(QIcon("resources/icon.png"))
    
    try:
        # 创建主窗口
        window = MainWindow()
        window.app = app  # 传递app引用用于剪贴板操作
        window.show()
        
        logger.info("应用启动成功")
        
        # 运行应用
        return app.exec()
        
    except Exception as e:
        logger.error(f"应用启动失败: {e}")
        QMessageBox.critical(None, "启动错误", f"应用启动失败:\n{str(e)}")
        return 1

if __name__ == "__main__":
    sys.exit(main())
