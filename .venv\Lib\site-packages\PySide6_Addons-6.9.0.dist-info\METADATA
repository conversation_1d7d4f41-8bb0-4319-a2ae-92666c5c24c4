Metadata-Version: 2.1
Name: PySide6_Addons
Version: 6.9.0
Summary: Python bindings for the Qt cross-platform application and UI framework (Addons)
Author-email: Qt for Python Team <<EMAIL>>
License: LGPL-3.0-only OR GPL-2.0-only OR GPL-3.0-only
Project-URL: Homepage, https://pyside.org
Project-URL: Documentation, https://doc.qt.io/qtforpython
Project-URL: Repository, https://code.qt.io/cgit/pyside/pyside-setup.git/
Project-URL: Changelog, https://code.qt.io/cgit/pyside/pyside-setup.git/tree/doc/changelogs
Project-URL: Tracker, https://bugreports.qt.io/projects/PYSIDE
Keywords: Qt
Classifier: Development Status :: 5 - Production/Stable
Classifier: Environment :: Console
Classifier: Environment :: MacOS X
Classifier: Environment :: X11 Applications :: Qt
Classifier: Environment :: Win32 (MS Windows)
Classifier: Intended Audience :: Developers
Classifier: License :: OSI Approved :: GNU Lesser General Public License v3 (LGPLv3)
Classifier: License :: OSI Approved :: GNU General Public License v3 (GPLv3)
Classifier: License :: OSI Approved :: GNU General Public License v2 (GPLv2)
Classifier: License :: Other/Proprietary License
Classifier: Operating System :: MacOS :: MacOS X
Classifier: Operating System :: POSIX
Classifier: Operating System :: POSIX :: Linux
Classifier: Operating System :: Microsoft
Classifier: Operating System :: Microsoft :: Windows
Classifier: Programming Language :: C++
Classifier: Programming Language :: Python
Classifier: Programming Language :: Python :: 3
Classifier: Programming Language :: Python :: 3.9
Classifier: Programming Language :: Python :: 3.10
Classifier: Programming Language :: Python :: 3.11
Classifier: Programming Language :: Python :: 3.12
Classifier: Programming Language :: Python :: 3.13
Classifier: Topic :: Database
Classifier: Topic :: Software Development
Classifier: Topic :: Software Development :: Code Generators
Classifier: Topic :: Software Development :: Libraries :: Application Frameworks
Classifier: Topic :: Software Development :: User Interfaces
Classifier: Topic :: Software Development :: Widget Sets
Requires-Python: <3.14,>=3.9
Description-Content-Type: text/markdown
License-File: LicenseRef-Qt-Commercial.txt
Requires-Dist: shiboken6 ==6.9.0
Requires-Dist: PySide6-Essentials ==6.9.0

# PySide6 Addons

PySide6 is the official Python module from the
[Qt for Python project](https://wiki.qt.io/Qt_for_Python),
which provides access to the complete Qt 6.0+ framework.

The Qt for Python project is developed in the open, with all facilities you'd expect
from any modern OSS project such as all code in a git repository and an open
design process. We welcome any contribution conforming to the
[Qt Contribution Agreement](https://www.qt.io/contributionagreement/).

This is a complementary wheel for [PySide6](https://pypi.org/project/PySide6),
it includes the following Qt modules:

* Qt3DAnimation
* Qt3DCore
* Qt3DExtras
* Qt3DInput
* Qt3DLogic
* Qt3DRender
* QtAxContainer
* QtBluetooth
* QtCharts
* QtDataVisualization
* QtGraphs
* QtGraphsWidgets
* QtMultimedia
* QtMultimediaWidgets
* QtNetworkAuth
* QtNfc
* QtPositioning
* QtQuick3D
* QtRemoteObjects
* QtScxml
* QtSensors
* QtSerialPort
* QtSerialBus
* QtSpatialAudio
* QtStateMachine
* QtTextToSpeech
* QtVirtualKeyboard
* QtWebChannel
* QtWebEngineCore
* QtWebEngineQuick
* QtWebEngineWidgets
* QtWebSockets
* QtPdf
* QtPdfWidgets
* QtHttpServer
* QtLocation
* QtAsyncio
* QtWebView

### Documentation and Bugs

You can find more information about the PySide6 module API in the
[official Qt for Python documentation](https://doc.qt.io/qtforpython/).

If you come across any issue, please file a bug report at our
[JIRA tracker](https://bugreports.qt.io/projects/PYSIDE) following
our [guidelines](https://wiki.qt.io/Qt_for_Python/Reporting_Bugs).

### Community

Check our channels on IRC (Libera), Telegram, Gitter, Matrix, and mailing list,
and [join our community](https://wiki.qt.io/Qt_for_Python#Community)!

### Licensing

PySide6 is available under both Open Source (LGPLv3 or GPLv2 or GPLv3) and commercial
license. Using PyPi is the recommended installation source, because the
content of the wheels is valid for both cases. For more information, refer to
the [Qt Licensing page](https://www.qt.io/licensing/).
