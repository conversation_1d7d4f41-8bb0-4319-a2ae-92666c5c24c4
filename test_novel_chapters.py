"""
测试小说创作工作室章节管理功能
"""

import sys
import os

# 添加项目路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_chapter_parsing():
    """测试章节解析功能"""
    print("=== 测试章节解析功能 ===")
    
    # 模拟不同格式的章节规划文本
    test_cases = [
        # 测试用例1：标准格式
        """
        【第一章：初入江湖】
        主要内容：主角踏入江湖，遇到第一个挑战
        
        【第二章：师父传艺】
        主要内容：拜师学艺，掌握基本功法
        
        【第三章：初试身手】
        主要内容：第一次实战，展现所学
        """,
        
        # 测试用例2：简单格式
        """
        第一章：开端
        第二章：发展
        第三章：高潮
        第四章：结局
        """,
        
        # 测试用例3：数字格式
        """
        1. 序章：命运的开始
        2. 觉醒：力量的苏醒
        3. 试炼：第一次考验
        4. 成长：实力的提升
        """,
        
        # 测试用例4：Markdown格式
        """
        ## 第一章 新的开始
        故事从这里开始...
        
        ## 第二章 意外的相遇
        主角遇到了重要的人...
        
        ## 第三章 危机降临
        突如其来的挑战...
        """,
        
        # 测试用例5：混合格式
        """
        Chapter 1: The Beginning
        这是第一章的内容
        
        【第二章：转折点】
        故事的转折
        
        3. 第三章：高潮部分
        最激烈的部分
        """
    ]
    
    # 导入解析函数
    from ui.novel_window import NovelWindow

    # 创建临时窗口实例来测试解析功能
    from PySide6.QtWidgets import QApplication, QListWidget
    from PySide6.QtCore import Qt
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    # 创建测试实例
    window = NovelWindow()
    
    for i, test_text in enumerate(test_cases, 1):
        print(f"\n--- 测试用例 {i} ---")
        print("输入文本:")
        print(test_text.strip())
        
        # 解析章节
        window.parse_chapters(test_text)
        
        print(f"\n解析结果 ({window.chapter_list.count()} 个章节):")
        for j in range(window.chapter_list.count()):
            item = window.chapter_list.item(j)
            chapter_num = item.data(Qt.UserRole) if item.data(Qt.UserRole) else "无"
            print(f"  {j+1}. {item.text()} (编号: {chapter_num})")
        
        print("-" * 50)

def test_chapter_management():
    """测试章节管理功能"""
    print("\n=== 测试章节管理功能 ===")
    
    from PySide6.QtWidgets import QApplication
    from PySide6.QtCore import Qt
    
    app = QApplication.instance()
    if app is None:
        app = QApplication(sys.argv)
    
    from ui.novel_window import NovelWindow
    window = NovelWindow()
    
    # 测试章节解析和选择
    test_text = """
    【第一章：开端】
    故事的开始
    
    【第二章：发展】
    情节的发展
    
    【第三章：高潮】
    故事的高潮
    """
    
    print("1. 解析章节...")
    window.parse_chapters(test_text)
    print(f"   解析到 {window.chapter_list.count()} 个章节")
    
    print("2. 测试章节选择...")
    if hasattr(window, 'chapter_selector'):
        print(f"   章节选择器选项数: {window.chapter_selector.count()}")
        for i in range(window.chapter_selector.count()):
            print(f"     {i}: {window.chapter_selector.itemText(i)}")
    
    print("3. 测试章节内容管理...")
    # 模拟选择第一章
    if window.chapter_list.count() > 0:
        first_item = window.chapter_list.item(0)
        window.edit_chapter(first_item)
        print(f"   当前选择章节: {window.current_chapter_label.text()}")
        
        # 模拟添加内容
        test_content = "这是第一章的测试内容..."
        window.chapter_content.setPlainText(test_content)
        
        # 保存章节
        window.save_current_chapter()
        print("   章节内容已保存")
        
        # 测试内容加载
        window.load_chapter_content(first_item.text())
        loaded_content = window.chapter_content.toPlainText()
        print(f"   加载的内容: {loaded_content[:20]}...")

def main():
    """主测试函数"""
    print("🔍 小说创作工作室章节管理功能测试")
    print("=" * 60)
    
    try:
        # 测试章节解析
        test_chapter_parsing()
        
        # 测试章节管理
        test_chapter_management()
        
        print("\n" + "=" * 60)
        print("✅ 所有测试完成！")
        
        print("\n📋 测试总结:")
        print("1. 章节解析功能 - 支持多种格式")
        print("2. 章节选择功能 - 下拉框和列表同步")
        print("3. 章节内容管理 - 保存和加载功能")
        print("4. 错误处理 - 无章节时创建默认章节")
        
    except Exception as e:
        print(f"\n❌ 测试过程中出现错误: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
