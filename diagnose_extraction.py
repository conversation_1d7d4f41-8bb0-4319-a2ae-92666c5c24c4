"""
诊断图片提取功能
"""

import sys
import os

def check_imports():
    """检查导入"""
    print("=== 检查导入 ===")
    
    try:
        from PySide6.QtWidgets import QApplication
        print("✅ PySide6.QtWidgets 导入成功")
    except ImportError as e:
        print(f"❌ PySide6.QtWidgets 导入失败: {e}")
        return False
    
    try:
        from PySide6.QtCore import QObject, Signal
        print("✅ PySide6.QtCore 导入成功")
    except ImportError as e:
        print(f"❌ PySide6.QtCore 导入失败: {e}")
        return False
    
    try:
        from core.webengine_setup import create_safe_webview
        print("✅ core.webengine_setup 导入成功")
    except ImportError as e:
        print(f"❌ core.webengine_setup 导入失败: {e}")
        return False
    
    try:
        from core.image_extractor import ImageExtractor
        print("✅ core.image_extractor 导入成功")
    except ImportError as e:
        print(f"❌ core.image_extractor 导入失败: {e}")
        return False
    
    return True

def check_webengine():
    """检查WebEngine"""
    print("\n=== 检查WebEngine ===")
    
    try:
        from PySide6.QtWebEngineWidgets import QWebEngineView
        print("✅ QWebEngineView 可用")
        return True
    except ImportError as e:
        print(f"❌ QWebEngineView 不可用: {e}")
        return False

def check_image_extractor():
    """检查ImageExtractor类"""
    print("\n=== 检查ImageExtractor类 ===")
    
    try:
        from core.image_extractor import ImageExtractor
        
        # 创建实例
        extractor = ImageExtractor()
        print("✅ ImageExtractor 实例创建成功")
        
        # 检查方法
        methods = [
            'extract_images_from_page',
            '_extract_all_images',
            '_extract_generated_images',
            '_extract_latest_image',
            '_extract_visible_images'
        ]
        
        for method in methods:
            if hasattr(extractor, method):
                print(f"✅ 方法 {method} 存在")
            else:
                print(f"❌ 方法 {method} 不存在")
        
        # 检查信号
        signals = ['image_extracted', 'extraction_progress', 'extraction_error']
        for signal in signals:
            if hasattr(extractor, signal):
                print(f"✅ 信号 {signal} 存在")
            else:
                print(f"❌ 信号 {signal} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ ImageExtractor 检查失败: {e}")
        return False

def check_ui_integration():
    """检查UI集成"""
    print("\n=== 检查UI集成 ===")
    
    try:
        from ui.image_generator_window import ImageGeneratorWindow
        print("✅ ImageGeneratorWindow 导入成功")
        
        # 检查方法
        methods = [
            'extract_images',
            'on_image_extracted',
            'on_extraction_progress',
            'on_extraction_error',
            'set_extraction_buttons_enabled'
        ]
        
        for method in methods:
            if hasattr(ImageGeneratorWindow, method):
                print(f"✅ UI方法 {method} 存在")
            else:
                print(f"❌ UI方法 {method} 不存在")
        
        return True
        
    except Exception as e:
        print(f"❌ UI集成检查失败: {e}")
        return False

def test_javascript_code():
    """测试JavaScript代码"""
    print("\n=== 测试JavaScript代码 ===")
    
    try:
        from core.image_extractor import ImageExtractor
        extractor = ImageExtractor()
        
        # 检查是否有JavaScript代码定义
        if hasattr(extractor, '_extract_all_images'):
            print("✅ _extract_all_images 方法存在")
        
        if hasattr(extractor, '_extract_generated_images'):
            print("✅ _extract_generated_images 方法存在")
        
        print("✅ JavaScript代码结构正常")
        return True
        
    except Exception as e:
        print(f"❌ JavaScript代码测试失败: {e}")
        return False

def main():
    """主函数"""
    print("🔍 图片提取功能诊断工具")
    print("=" * 50)
    
    # 添加项目路径
    sys.path.append(os.path.dirname(os.path.abspath(__file__)))
    
    results = []
    
    # 检查导入
    results.append(check_imports())
    
    # 检查WebEngine
    results.append(check_webengine())
    
    # 检查ImageExtractor
    results.append(check_image_extractor())
    
    # 检查UI集成
    results.append(check_ui_integration())
    
    # 测试JavaScript
    results.append(test_javascript_code())
    
    # 总结
    print("\n" + "=" * 50)
    print("📊 诊断结果总结:")
    
    passed = sum(results)
    total = len(results)
    
    print(f"通过: {passed}/{total}")
    
    if passed == total:
        print("🎉 所有检查都通过！图片提取功能应该可以正常工作。")
    else:
        print("⚠️ 存在问题，需要修复。")
        
        print("\n🔧 建议的修复步骤:")
        print("1. 确保所有依赖都已正确安装")
        print("2. 检查文件路径和导入语句")
        print("3. 验证WebEngine组件是否可用")
        print("4. 重新启动程序")

if __name__ == "__main__":
    main()
    input("\n按回车键退出...")
