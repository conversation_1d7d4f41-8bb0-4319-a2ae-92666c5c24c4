# 高级图片提取功能测试指南

## 🎯 测试目标

验证新的高级图片提取功能能够正确识别、下载和管理网页中的图片，特别是AI生成的图片。

## 🚀 测试准备

### 1. 启动程序
```bash
python main.py
```

### 2. 打开分镜图片生成器
- 在主界面点击 "工具" → "分镜图片生成"
- 等待豆包网页加载完成

### 3. 检查新功能界面
在豆包生图标签页应该看到新的按钮：
- ✅ "提取所有图片"
- ✅ "提取AI图片" 
- ✅ "提取最新图片"
- ✅ "从剪贴板导入"（原有功能）

## 📋 详细测试步骤

### 测试1: 提取所有图片功能

#### 测试步骤
1. **准备测试页面**
   - 在豆包网页中导航到包含图片的页面
   - 或者访问任何包含多张图片的网站

2. **执行提取**
   - 点击"提取所有图片"按钮
   - 观察状态栏的进度提示

3. **验证结果**
   - 检查是否自动切换到"生成结果"标签页
   - 验证图片是否正确显示
   - 检查图片库是否新增记录

#### 预期结果
- ✅ 状态栏显示"正在扫描页面图片..."
- ✅ 显示找到的图片数量
- ✅ 逐个下载并显示进度
- ✅ 成功的图片自动添加到图片库
- ✅ 图片信息包含提取方式、尺寸等详细信息

### 测试2: AI图片智能识别

#### 测试步骤
1. **生成AI图片**
   - 在豆包网页中输入提示词
   - 等待AI图片生成完成

2. **智能提取**
   - 点击"提取AI图片"按钮
   - 观察识别和提取过程

3. **验证识别准确性**
   - 检查是否只提取了AI生成的图片
   - 验证是否过滤了其他无关图片

#### 预期结果
- ✅ 状态栏显示"正在识别AI生成图片..."
- ✅ 只提取符合AI特征的图片
- ✅ 图片信息标注"AI生成图片"
- ✅ 图片库中显示"(AI生成)"标识

### 测试3: 最新图片提取

#### 测试步骤
1. **生成新图片**
   - 在豆包中生成一张新图片
   - 确保页面中有多张图片

2. **提取最新**
   - 点击"提取最新图片"按钮
   - 验证是否提取了最新的图片

#### 预期结果
- ✅ 状态栏显示"正在查找最新图片..."
- ✅ 只提取最新添加的图片
- ✅ 图片信息标注"最新图片"
- ✅ 图片库中显示"(最新)"标识

### 测试4: 错误处理机制

#### 测试场景A: WebEngine不可用
1. **模拟条件**: 如果WebEngine不可用
2. **执行操作**: 点击任意提取按钮
3. **预期结果**: 显示友好的错误提示和替代方案

#### 测试场景B: 网页无图片
1. **测试页面**: 访问纯文本页面
2. **执行操作**: 点击"提取所有图片"
3. **预期结果**: 提示"页面中没有找到符合条件的图片"

#### 测试场景C: 网络问题
1. **模拟条件**: 断开网络连接
2. **执行操作**: 尝试提取网络图片
3. **预期结果**: 显示网络错误提示

### 测试5: 图片格式支持

#### 测试不同格式
1. **PNG图片** - 测试PNG格式图片提取
2. **JPEG图片** - 测试JPEG格式图片提取
3. **WebP图片** - 测试WebP格式图片提取
4. **GIF图片** - 测试GIF格式图片提取

#### 预期结果
- ✅ 所有主流格式都能正确提取
- ✅ 格式信息正确记录
- ✅ 图片质量保持原始水平

### 测试6: 批量处理性能

#### 大量图片测试
1. **访问图片较多的页面** (10+张图片)
2. **执行批量提取** - 点击"提取所有图片"
3. **观察性能表现**

#### 预期结果
- ✅ 界面保持响应，不卡顿
- ✅ 进度条正确显示处理进度
- ✅ 内存使用合理，无泄漏
- ✅ 所有图片都能成功处理

## 🔍 功能验证清单

### 基础功能
- [ ] 提取所有图片功能正常
- [ ] AI图片识别准确
- [ ] 最新图片提取正确
- [ ] 状态反馈及时准确

### 图片处理
- [ ] Base64图片正确处理
- [ ] 网络图片成功下载
- [ ] Blob URL正确转换
- [ ] 跨域图片处理正常

### 用户界面
- [ ] 按钮响应正常
- [ ] 进度显示准确
- [ ] 错误提示友好
- [ ] 结果展示完整

### 数据管理
- [ ] 图片库正确更新
- [ ] 图片信息完整记录
- [ ] 文件保存成功
- [ ] 临时文件管理正常

### 错误处理
- [ ] WebEngine不可用时的处理
- [ ] 网络错误的处理
- [ ] 无图片页面的处理
- [ ] 格式不支持的处理

## 🎨 实际使用场景测试

### 场景1: 豆包生图完整工作流

1. **输入提示词**: "一个古装美女推门而入，中景构图"
2. **等待生成**: 豆包生成图片
3. **智能提取**: 点击"提取AI图片"
4. **查看结果**: 验证图片质量和信息
5. **保存管理**: 检查图片库记录

### 场景2: 多图片批量收集

1. **访问图片网站**: 如摄影网站、设计网站
2. **批量提取**: 点击"提取所有图片"
3. **筛选结果**: 在图片库中查看所有图片
4. **导出使用**: 测试图片导出功能

### 场景3: 实时内容监控

1. **动态页面**: 访问会实时更新图片的页面
2. **监控最新**: 定期点击"提取最新图片"
3. **对比结果**: 验证是否获取了最新内容

## 📊 性能基准测试

### 响应时间测试
- **小图片(< 1MB)**: 提取时间 < 2秒
- **中等图片(1-5MB)**: 提取时间 < 5秒
- **大图片(> 5MB)**: 提取时间 < 10秒

### 批量处理测试
- **5张图片**: 总时间 < 10秒
- **10张图片**: 总时间 < 20秒
- **20张图片**: 总时间 < 40秒

### 内存使用测试
- **空闲状态**: 内存使用 < 100MB
- **处理中**: 内存增长 < 50MB
- **处理完成**: 内存自动释放

## 🛠️ 问题排查

### 如果提取失败

1. **检查WebEngine状态**
   ```bash
   python diagnose_webengine.py
   ```

2. **查看详细日志**
   ```bash
   type logs\app.log
   ```

3. **测试网络连接**
   - 确保网络正常
   - 检查防火墙设置

4. **重启程序**
   - 关闭程序重新启动
   - 清理临时文件

### 常见问题解决

#### 问题1: 按钮无响应
**解决**: 检查WebEngine是否正确初始化

#### 问题2: 图片提取不完整
**解决**: 等待页面完全加载后再提取

#### 问题3: AI识别不准确
**解决**: 使用"提取所有图片"后手动筛选

## 🎉 测试完成标准

当以下所有项目都通过时，表示功能测试成功：

### ✅ 核心功能测试
- 所有提取模式都能正常工作
- 图片识别准确率 > 90%
- 下载成功率 > 95%
- 错误处理机制完善

### ✅ 性能测试
- 界面响应流畅，无卡顿
- 内存使用合理，无泄漏
- 批量处理稳定可靠

### ✅ 用户体验测试
- 操作直观简单
- 反馈信息及时准确
- 错误提示友好明确

---

**开始测试**: 按照上述步骤逐项测试，验证高级图片提取功能的完整性和可靠性！

**重点测试**: 特别关注"提取AI图片"功能，这是针对豆包等AI生图网站的核心功能。
